# 用户端算价页面修复验证测试

## 测试目标

验证以下两个问题的修复效果：
1. 下一步按钮状态控制问题
2. 算价接口state参数问题

## 测试环境准备

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 确保数据库数据
- 确保有已发布的盒型数据
- 确保有用户账号可以登录
- 确保相关API接口正常工作

## 测试步骤

### 测试1：页面访问和初始化

1. **访问报价页面**
   - 打开浏览器访问 `http://localhost:3000/quote`
   - 登录用户账号
   - 验证页面正常显示盒型列表

2. **跳转到算价页面**
   - 点击任意盒型卡片
   - 验证是否正确跳转到算价页面
   - 检查URL是否包含 `boxId` 参数
   - 验证页面是否正常加载和初始化

### 测试2：步骤验证逻辑测试

1. **基础信息步骤**
   - 进入算价页面后，应该在第一步（基础信息）
   - 检查下一步按钮状态：
     - 如果数量为空或0，下一步按钮应该禁用
     - 填写有效数量后，下一步按钮应该启用
   - 测试动态属性填写验证

2. **材料选择步骤**
   - 点击下一步进入材料选择步骤
   - 检查下一步按钮状态：
     - 未进行拼版计算时，下一步按钮应该禁用
     - 完成拼版计算后，下一步按钮应该启用

3. **后续步骤**
   - 工艺选择、配件选择、加工费用步骤应该可以直接通过
   - 验证上一步按钮始终可用

### 测试3：算价接口调用测试

1. **手动触发算价**
   - 在任意步骤点击"重新计算"按钮
   - 打开浏览器开发者工具，查看Network标签
   - 验证API调用是否成功

2. **检查API请求参数**
   - 查看 `/api/v1/calculation/engine/generateQuotation` 请求
   - 验证请求体格式是否为 `{ state: {...} }`
   - 确认不再出现 `state` 参数为 `undefined` 的错误

3. **检查API响应**
   - 验证API返回成功响应
   - 检查报价数据是否正确显示在右侧汇总面板

### 测试4：完整流程测试

1. **完整算价流程**
   - 从基础信息开始，逐步完成所有步骤
   - 验证每个步骤的验证逻辑
   - 确认只有满足条件才能进入下一步

2. **报价生成测试**
   - 完成所有必要步骤后进入报价确认页面
   - 验证报价数据是否正确显示
   - 测试保存报价功能

## 预期结果

### ✅ 修复成功的标志

1. **步骤验证正常**
   - 下一步按钮根据步骤完成状态正确启用/禁用
   - 不能跳过未完成的步骤
   - 可以正常返回上一步

2. **API调用成功**
   - 算价接口调用成功，无参数错误
   - 返回正确的报价数据
   - 右侧汇总面板正常显示费用信息

3. **用户体验良好**
   - 页面操作流畅，无卡顿
   - 错误提示友好明确
   - 步骤导航逻辑清晰

### ❌ 需要进一步修复的问题

如果出现以下情况，需要进一步调试：

1. **步骤验证问题**
   - 下一步按钮状态不正确
   - 可以跳过未完成的步骤
   - 验证逻辑不生效

2. **API调用问题**
   - 仍然出现 `state` 参数 `undefined` 错误
   - API返回错误响应
   - 报价数据显示异常

3. **其他问题**
   - 页面加载错误
   - 组件渲染异常
   - 控制台出现错误信息

## 调试方法

### 1. 浏览器开发者工具
- **Console标签**：查看JavaScript错误和调试信息
- **Network标签**：监控API请求和响应
- **Elements标签**：检查DOM结构和样式

### 2. 代码调试
- 在关键函数中添加 `console.log` 输出
- 检查状态变化和参数传递
- 验证Hook返回值

### 3. 后端日志
- 查看服务器控制台输出
- 检查API接口接收到的参数
- 验证后端处理逻辑

## 测试报告模板

```
测试时间：[填写测试时间]
测试人员：[填写测试人员]
测试环境：[开发环境/生产环境]

测试结果：
□ 页面访问正常
□ 步骤验证逻辑正确
□ API调用成功
□ 完整流程通过

发现问题：
[描述发现的问题]

修复建议：
[提出修复建议]
```

## 注意事项

1. **测试数据**：确保数据库中有足够的测试数据
2. **网络环境**：确保网络连接稳定
3. **浏览器兼容性**：建议使用Chrome或Firefox进行测试
4. **缓存清理**：测试前清理浏览器缓存，确保加载最新代码
