'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, Steps, Button, Space, message, Spin, Row, Col } from 'antd';
import {
  FileTextOutlined,
  SettingOutlined,
  ToolOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';

// 导入管理后台的算价组件和类型
import {
  CalculationState,
  CalculationStep,
  BoxBasicInfo,
  PackagingConfig,
  MaterialConfig,
  ProcessConfig,
  AccessoryConfig,
  ProcessingFeeConfig,
  FormulaConfig
} from '@/app/admin/box/calculation/types/calculation';
import { useCalculationState } from '@/app/admin/box/calculation/hooks/useCalculationState';
import { useStepNavigation } from '@/app/admin/box/calculation/hooks/useStepNavigation';
import { calculationEngineApi } from '@/services/calculationApi';

// 导入步骤组件（需要适配为用户端版本）
import UserBasicInfoStep from './steps/UserBasicInfoStep';
import UserPackagingStep from './steps/UserPackagingStep';
import UserProcessStep from './steps/UserProcessStep';
import UserAccessoryStep from './steps/UserAccessoryStep';
import UserProcessingFeeStep from './steps/UserProcessingFeeStep';
import UserQuotationStep from './steps/UserQuotationStep';
import DebugPanel from './DebugPanel';

// 导入用户端的 CalculationSummary 组件
import UserCalculationSummary from './UserCalculationSummary';

import { Box } from '@/types/box';
import { CurrentUser } from '@/types/user';
import { QuotationDetail } from '@/app/admin/box/calculation/types/calculation';

interface UserCalculationWizardProps {
  sourceBox?: Box | null;
  user?: CurrentUser | null;
}

/**
 * 用户端算价向导组件
 * 复用管理后台算价逻辑，适配前端设计风格
 */
export default function UserCalculationWizard({ sourceBox, user }: UserCalculationWizardProps) {
  const {
    state,
    updateBasicInfo,
    updatePartConfig,
    updatePackagingConfig,
    updateMaterialConfig,
    updateProcessConfig,
    updateAccessoryConfig,
    updateProcessingFeeConfig,
    updateFormulaConfig,
    updateQuotation,
    setCurrentStep,
    resetState,
    getStepValidation
  } = useCalculationState();

  const [isCalculating, setIsCalculating] = useState(false);
  const [quotation, setQuotation] = useState<QuotationDetail | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 使用步骤导航Hook
  const stepNavigation = useStepNavigation({
    currentStep: state.currentStep,
    onStepChange: setCurrentStep,
    getStepValidation
  });

  // 确保状态完全初始化
  useEffect(() => {
    if (state && state.basicInfo) {
      setIsInitialized(true);
    }
  }, [state]);

  // 步骤配置
  const steps = [
    {
      title: '基础信息',
      icon: <FileTextOutlined />,
      description: '确认盒型信息和填写基本参数',
      key: CalculationStep.BASIC_INFO
    },
    {
      title: '材料选择',
      icon: <SettingOutlined />,
      description: '选择材料和拼版配置',
      key: CalculationStep.PACKAGING
    },
    {
      title: '工艺选择',
      icon: <ToolOutlined />,
      description: '选择印刷和后道工艺',
      key: CalculationStep.PROCESS
    },
    {
      title: '配件选择',
      icon: <AppstoreOutlined />,
      description: '选择包装配件',
      key: CalculationStep.ACCESSORY
    },
    {
      title: '加工费用',
      icon: <DollarOutlined />,
      description: '选择加工费用项目',
      key: CalculationStep.PROCESSING_FEE
    },
    {
      title: '确认报价',
      icon: <CheckCircleOutlined />,
      description: '查看详细报价单',
      key: CalculationStep.QUOTATION
    }
  ];

  // 获取当前步骤索引
  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.key === state.currentStep);
  };

  // 防止无限循环的标记
  const isCalculatingRef = useRef(false);
  const lastCalculationHashRef = useRef<string>('');

  // 生成计算状态的哈希值，用于检测是否需要重新计算
  const generateCalculationHash = useCallback(() => {
    if (!state || !state.basicInfo) return '';

    return JSON.stringify({
      quantity: state.basicInfo.quantity,
      attributes: state.basicInfo.attributes,
      parts: state.basicInfo.parts,
      materialConfig: state.materialConfig,
      processConfig: state.processConfig,
      accessoryConfig: state.accessoryConfig,
      processingFeeConfig: state.processingFeeConfig,
      formulaConfig: state.formulaConfig
    });
  }, [state]);

  // 自动重新计算报价 - 添加防重复计算逻辑
  const recalculateQuotation = useCallback(async () => {
    try {
      // 防止重复计算
      if (isCalculatingRef.current) {
        console.log('正在计算中，跳过重复请求');
        return;
      }

      // 检查状态是否完整
      if (!state || !state.basicInfo) {
        console.warn('计算状态不完整，跳过报价计算');
        return;
      }

      // 确保基础信息完整
      if (!state.basicInfo.quantity || state.basicInfo.quantity <= 0) {
        console.warn('数量信息无效:', state.basicInfo.quantity);
        return;
      }

      // 生成当前状态哈希，检查是否需要重新计算
      const currentHash = generateCalculationHash();
      if (currentHash === lastCalculationHashRef.current) {
        console.log('状态未变化，跳过重复计算');
        return;
      }

      console.log('开始计算报价，状态哈希:', currentHash);
      isCalculatingRef.current = true;
      setIsCalculating(true);

      // 调用用户端的计算引擎API - 修复参数格式
      const result = await calculationEngineApi.generateQuotation({ state });
      if (result.success && result.data) {
        setQuotation(result.data);
        updateQuotation(result.data);
        lastCalculationHashRef.current = currentHash;
        console.log('报价计算完成');
      } else {
        message.error(result.error?.message || '计算失败');
      }
    } catch (error) {
      console.error('计算错误:', error);
      message.error('计算失败，请检查配置');
    } finally {
      isCalculatingRef.current = false;
      setIsCalculating(false);
    }
  }, [state, updateQuotation, generateCalculationHash]);

  // 下一步 - 使用步骤验证逻辑
  const handleNext = () => {
    stepNavigation.nextStep();
  };

  // 上一步 - 使用步骤验证逻辑
  const handlePrev = () => {
    stepNavigation.previousStep();
  };

  // 重置所有数据
  const handleReset = () => {
    resetState();
    setQuotation(null);
    message.info('已重置所有数据');
  };

  // 渲染当前步骤内容
  const renderStepContent = () => {
    const updateHandlers = {
      basicInfo: updateBasicInfo,
      partConfig: updatePartConfig,
      packagingConfig: updatePackagingConfig,
      materialConfig: updateMaterialConfig,
      processConfig: updateProcessConfig,
      accessoryConfig: updateAccessoryConfig,
      processingFeeConfig: updateProcessingFeeConfig,
      formulaConfig: updateFormulaConfig
    };

    switch (state.currentStep) {
      case CalculationStep.BASIC_INFO:
        return (
          <UserBasicInfoStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
            sourceBox={sourceBox}
          />
        );
      case CalculationStep.PACKAGING:
        return (
          <UserPackagingStep
            state={state}
            onUpdate={updateHandlers}
          />
        );
      case CalculationStep.PROCESS:
        return (
          <UserProcessStep
            state={state}
            onUpdate={updateHandlers}
          />
        );
      case CalculationStep.ACCESSORY:
        return (
          <UserAccessoryStep
            state={state}
            onUpdate={updateHandlers}
          />
        );
      case CalculationStep.PROCESSING_FEE:
        return (
          <UserProcessingFeeStep
            state={state}
            onUpdate={updateHandlers}
          />
        );
      case CalculationStep.QUOTATION:
        return (
          <UserQuotationStep
            state={state}
            quotation={quotation}
            user={user}
            onRecalculate={recalculateQuotation}
          />
        );
      default:
        return null;
    }
  };

  // 如果状态还没有初始化，显示加载状态
  if (!isInitialized) {
    return (
      <Card>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px'
        }}>
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}>正在初始化算价系统...</span>
        </div>
      </Card>
    );
  }

  // 如果没有传递盒型信息，显示提示
  if (!sourceBox) {
    return (
      <Card>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📦</div>
          <h3 style={{ color: '#fa8c16', marginBottom: '8px' }}>未选择盒型</h3>
          <p style={{ color: '#666', marginBottom: '16px' }}>
            请先从包装报价页面选择您需要的盒型，然后再进行算价计算
          </p>
          <Button type="primary" onClick={() => window.location.href = '/quote'}>
            返回选择盒型
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="user-calculation-wizard">
      {/* 步骤导航 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card style={{ marginBottom: 16 }}>
            <Steps
              current={getCurrentStepIndex()}
              direction="horizontal"
              size="small"
              items={steps.map(step => ({
                title: step.title,
                description: step.description,
                icon: step.icon
              }))}
              style={{ marginBottom: 0 }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 主要内容区域 */}
        <Col span={state.currentStep === CalculationStep.QUOTATION ? 24 : 18}>
          <Card
            title={`步骤 ${getCurrentStepIndex() + 1}: ${steps[getCurrentStepIndex()]?.title}`}
            loading={isCalculating}
            className="min-h-96"
          >
            {/* 步骤内容 */}
            {isCalculating ? (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px'
              }}>
                <Spin size="large" />
                <span style={{ marginLeft: 16 }}>正在计算报价...</span>
              </div>
            ) : (
              renderStepContent()
            )}
          </Card>
        </Col>

        {/* 侧边栏 - 费用汇总 */}
        {state.currentStep !== CalculationStep.QUOTATION && (
          <Col span={6}>
            <UserCalculationSummary
              quotation={state.quotation}
              basicInfo={state.basicInfo}
              isCalculating={isCalculating}
              onRecalculate={recalculateQuotation}
            />

            {/* 导航按钮 */}
            <Card style={{ marginTop: 16 }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: '8px'
              }}>
                <Button
                  onClick={handleReset}
                  icon={<ReloadOutlined />}
                >
                  重置
                </Button>

                <Space>
                  {stepNavigation.canGoPrevious && (
                    <Button onClick={handlePrev}>
                      上一步
                    </Button>
                  )}
                  {stepNavigation.canGoNext && (
                    <Button type="primary" onClick={handleNext}>
                      下一步
                    </Button>
                  )}
                </Space>
              </div>
            </Card>
          </Col>
        )}
      </Row>

      {/* 调试面板 - 仅开发环境 */}
      <DebugPanel
        state={state}
        quotation={quotation}
        onTestCalculation={recalculateQuotation}
      />
    </div>
  );
}
