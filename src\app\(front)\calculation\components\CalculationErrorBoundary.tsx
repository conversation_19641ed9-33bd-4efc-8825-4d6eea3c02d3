'use client';

import React from 'react';
import { Result, Button } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

interface CalculationErrorBoundaryProps {
  children: React.ReactNode;
}

interface CalculationErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

/**
 * 算价页面错误边界组件
 * 捕获和处理算价过程中的错误
 */
export default class CalculationErrorBoundary extends React.Component<
  CalculationErrorBoundaryProps,
  CalculationErrorBoundaryState
> {
  constructor(props: CalculationErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): CalculationErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('算价页面错误:', error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined });
    // 刷新页面
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '40px 20px', textAlign: 'center' }}>
          <Result
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="算价功能出现错误"
            subTitle="抱歉，算价过程中出现了错误。请尝试刷新页面或联系客服。"
            extra={[
              <Button type="primary" key="retry" onClick={this.handleReset}>
                重新开始
              </Button>,
              <Button key="contact" onClick={() => window.open('tel:18638728164')}>
                联系客服
              </Button>
            ]}
          />
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <div style={{ 
              marginTop: '20px', 
              padding: '16px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              textAlign: 'left',
              fontSize: '12px',
              color: '#666'
            }}>
              <strong>错误详情（开发模式）：</strong>
              <pre>{this.state.error.stack}</pre>
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}
