'use client';

import React from 'react';
import { Card } from 'antd';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';

// 直接复用管理后台的配件步骤组件
import AccessoryStep from '@/app/admin/box/calculation/components/steps/AccessoryStep';

interface UserAccessoryStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
}

/**
 * 用户端配件选择步骤组件
 * 复用管理后台组件，添加用户端样式适配
 */
export default function UserAccessoryStep({
  state,
  onUpdate
}: UserAccessoryStepProps) {
  return (
    <div className="user-accessory-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>💡 操作提示</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            根据包装需求选择相应的配件，如手提袋、缎带、泡沫等。配件会增加包装的美观度和实用性。
          </p>
        </div>
      </Card>

      {/* 复用管理后台的配件组件 */}
      <AccessoryStep
        state={state}
        onUpdate={onUpdate}
        onRecalculate={async () => {}} // 空异步函数，用户端不需要手动触发重新计算
      />
    </div>
  );
}
