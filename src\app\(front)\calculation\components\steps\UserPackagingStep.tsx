'use client';

import React from 'react';
import { Card } from 'antd';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';

// 直接复用管理后台的包装步骤组件
import PackagingStep from '@/app/admin/box/calculation/components/steps/PackagingStep';

interface UserPackagingStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
}

/**
 * 用户端材料选择步骤组件
 * 复用管理后台组件，添加用户端样式适配
 */
export default function UserPackagingStep({
  state,
  onUpdate
}: UserPackagingStepProps) {
  return (
    <div className="user-packaging-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>💡 操作提示</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            请选择合适的材料类型和规格。系统会自动计算最优的拼版方案和材料用量。
          </p>
        </div>
      </Card>

      {/* 复用管理后台的包装组件 */}
      <PackagingStep
        state={state}
        onUpdate={onUpdate}
      />
    </div>
  );
}
