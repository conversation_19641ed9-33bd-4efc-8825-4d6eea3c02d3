// 用户端算价API服务，封装所有与算价相关的后端交互
import { resultApi } from '@/lib/utils/request';
import {
  Box,
  BoxListParams
} from '@/types/box';
import {
  MaterialListParams,
  Paper,
  GreyBoard,
  Accessory,
  GiftBoxAccessory,
} from '@/types/material';
import {
  Printing,
  PrintingListParams,
  ProcessingFee,
  ProcessingFeeListParams,
  SurfaceProcess,
  SurfaceProcessListParams,
  LaminatingProcess,
  LaminatingProcessListParams,
  DieCuttingProcess,
  DieCuttingProcessListParams,
  HotStampingProcess,
  HotStampingProcessListParams,
} from '@/types/craftSalary';
import { PaginatedData, Result } from '@/types/common';

/**
 * 用户端算价API服务
 * 调用用户端专用的API接口，权限要求为已登录用户
 */

// 盒型相关API
export const calculationBoxApi = {
  /**
   * 获取盒型列表（用户端）
   */
  getList: (params: BoxListParams): Promise<Result<PaginatedData<Box>>> => {
    return resultApi.post<PaginatedData<Box>>('/api/v1/calculation/box/getList', params);
  },

  /**
   * 获取盒型详情（用户端）
   */
  getDetail: (id: number): Promise<Result<Box>> => {
    return resultApi.post<Box>('/api/v1/calculation/box/getDetail', { id });
  },

  /**
   * 获取盒型图片URL
   */
  getImageUrl: (id: number) => {
    return `/api/v1/admin/box/getImage?id=${id}`;
  },
};

// 材料相关API
export const calculationMaterialApi = {
  /**
   * 获取纸张材料列表
   */
  getPaperList: (params: MaterialListParams): Promise<Result<PaginatedData<Paper>>> => {
    return resultApi.post<PaginatedData<Paper>>('/api/v1/calculation/material/paper/getList', params);
  },

  /**
   * 获取灰板材料列表
   */
  getGreyBoardList: (params: MaterialListParams): Promise<Result<PaginatedData<GreyBoard>>> => {
    return resultApi.post<PaginatedData<GreyBoard>>('/api/v1/calculation/material/greyBoard/getList', params);
  },

  /**
   * 获取配件列表
   */
  getAccessoryList: (params: MaterialListParams): Promise<Result<PaginatedData<Accessory>>> => {
    return resultApi.post<PaginatedData<Accessory>>('/api/v1/calculation/material/accessory/getList', params);
  },

  /**
   * 获取礼盒配件列表
   */
  getGiftBoxAccessoryList: (params: MaterialListParams): Promise<Result<PaginatedData<GiftBoxAccessory>>> => {
    return resultApi.post<PaginatedData<GiftBoxAccessory>>('/api/v1/calculation/material/giftBoxAccessory/getList', params);
  },
};

// 工艺相关API
export const calculationCraftApi = {
  /**
   * 获取印刷工艺列表
   */
  getPrintingList: (params: PrintingListParams): Promise<Result<PaginatedData<Printing>>> => {
    return resultApi.post<PaginatedData<Printing>>('/api/v1/calculation/craft/printing/getList', params);
  },

  /**
   * 获取表面处理工艺列表
   */
  getSurfaceProcessList: (params: SurfaceProcessListParams): Promise<Result<PaginatedData<SurfaceProcess>>> => {
    return resultApi.post<PaginatedData<SurfaceProcess>>('/api/v1/calculation/craft/surfaceProcess/getList', params);
  },

  /**
   * 获取覆膜工艺列表
   */
  getLaminatingProcessList: (params: LaminatingProcessListParams): Promise<Result<PaginatedData<LaminatingProcess>>> => {
    return resultApi.post<PaginatedData<LaminatingProcess>>('/api/v1/calculation/craft/laminatingProcess/getList', params);
  },

  /**
   * 获取模切工艺列表
   */
  getDieCuttingProcessList: (params: DieCuttingProcessListParams): Promise<Result<PaginatedData<DieCuttingProcess>>> => {
    return resultApi.post<PaginatedData<DieCuttingProcess>>('/api/v1/calculation/craft/dieCuttingProcess/getList', params);
  },

  /**
   * 获取烫金工艺列表
   */
  getHotStampingProcessList: (params: HotStampingProcessListParams): Promise<Result<PaginatedData<HotStampingProcess>>> => {
    return resultApi.post<PaginatedData<HotStampingProcess>>('/api/v1/calculation/craft/hotStampingProcess/getList', params);
  },

  /**
   * 获取加工费列表
   */
  getProcessingFeeList: (params: ProcessingFeeListParams): Promise<Result<PaginatedData<ProcessingFee>>> => {
    return resultApi.post<PaginatedData<ProcessingFee>>('/api/v1/calculation/craft/processingFee/getList', params);
  },
};

// 计算相关API
export const calculationEngineApi = {
  /**
   * 计算材料成本
   */
  calculateMaterialCost: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/engine/calculateMaterialCost', params);
  },

  /**
   * 计算工艺成本
   */
  calculateProcessCost: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/engine/calculateProcessCost', params);
  },

  /**
   * 计算配件成本
   */
  calculateAccessoryCost: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/engine/calculateAccessoryCost', params);
  },

  /**
   * 生成最终报价
   */
  generateQuotation: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/engine/generateQuotation', params);
  },
};

// 用户报价相关API
export const userQuotationApi = {
  /**
   * 保存用户报价
   */
  saveQuotation: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/quotation/save', params);
  },

  /**
   * 获取用户报价列表
   */
  getQuotationList: (params: any): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/quotation/getList', params);
  },

  /**
   * 获取用户报价详情
   */
  getQuotationDetail: (id: number): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/quotation/getDetail', { id });
  },

  /**
   * 删除用户报价
   */
  deleteQuotation: (id: number): Promise<Result<any>> => {
    return resultApi.post<any>('/api/v1/calculation/quotation/delete', { id });
  },
};
