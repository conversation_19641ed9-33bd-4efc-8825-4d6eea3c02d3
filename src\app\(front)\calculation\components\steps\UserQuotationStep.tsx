'use client';

import React from 'react';
import { Card, Button, Space, message } from 'antd';
import { DownloadOutlined, PrinterOutlined, CopyOutlined } from '@ant-design/icons';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';
import { QuotationDetail } from '@/app/admin/box/calculation/types/calculation';
import { CurrentUser } from '@/types/user';

// 直接复用管理后台的报价步骤组件
import QuotationStep from '@/app/admin/box/calculation/components/steps/QuotationStep';

interface UserQuotationStepProps {
  state: CalculationState;
  quotation: QuotationDetail | null;
  user?: CurrentUser | null;
  onRecalculate: () => void;
}

/**
 * 用户端报价确认步骤组件
 * 复用管理后台组件，添加用户端样式适配和额外功能
 */
export default function UserQuotationStep({
  state,
  quotation,
  user,
  onRecalculate
}: UserQuotationStepProps) {
  
  // 保存报价到用户中心
  const handleSaveQuotation = async () => {
    try {
      if (!quotation) {
        message.error('暂无报价数据可保存');
        return;
      }

      // 调用保存报价的API
      const { userQuotationApi } = await import('@/services/calculationApi');
      const result = await userQuotationApi.saveQuotation({
        quotation,
        calculationState: state
      });

      if (result.success) {
        message.success('报价已保存到您的用户中心');
      } else {
        message.error(result.error?.message || '保存报价失败');
      }
    } catch (error) {
      console.error('保存报价失败:', error);
      message.error('保存报价失败');
    }
  };

  return (
    <div className="user-quotation-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>🎉 报价完成</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            恭喜！您的包装报价已生成完成。您可以保存报价、下载PDF或联系我们的客服进行进一步咨询。
          </p>
        </div>
      </Card>

      {/* 用户端操作按钮 - 响应式设计 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <div style={{ minWidth: '200px' }}>
            <h4 style={{ margin: 0 }}>报价操作</h4>
            <p style={{ margin: '4px 0 0 0', color: '#666' }}>
              选择您需要的操作
            </p>
          </div>
          <Space wrap>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleSaveQuotation}
              size="middle"
            >
              保存到用户中心
            </Button>
          </Space>
        </div>
      </Card>

      {/* 复用管理后台的报价组件 */}
      <QuotationStep
        state={state}
        onUpdate={{
          formulaConfig: () => {} // 用户端不需要公式配置功能
        }}
        onRecalculate={onRecalculate}
      />

      {/* 用户端额外信息 */}
      <Card style={{ marginTop: 16, backgroundColor: '#fff7e6', border: '1px solid #ffd591' }}>
        <div style={{ color: '#fa8c16' }}>
          <h4 style={{ margin: 0, color: '#fa8c16' }}>📞 需要帮助？</h4>
          <p style={{ margin: '8px 0 0 0', color: '#fa8c16' }}>
            如果您对报价有任何疑问，或需要定制化服务，请联系我们的专业客服团队：
            <br />
            电话：18638728164
            <br />
            我们将为您提供专业的包装解决方案。
          </p>
        </div>
      </Card>
    </div>
  );
}
