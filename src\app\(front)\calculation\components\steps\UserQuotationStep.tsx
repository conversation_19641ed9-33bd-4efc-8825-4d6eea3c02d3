'use client';

import React, { useEffect } from 'react';
import { Card, Button, Space, message, Tooltip, Divider } from 'antd';
import { DownloadOutlined, PrinterOutlined, CopyOutlined, ReloadOutlined } from '@ant-design/icons';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';
import { QuotationDetail } from '@/app/admin/box/calculation/types/calculation';
import { CurrentUser } from '@/types/user';

// 直接复用管理后台的报价步骤组件
import QuotationStep from '@/app/admin/box/calculation/components/steps/QuotationStep';

// 导入PDF导出和打印功能
import {
  exportQuotationToPDFFromHTML,
  generateDetailedQuotationHTML,
  executePrintQuotation
} from '@/app/admin/box/calculation/utils/pdfExport';

interface UserQuotationStepProps {
  state: CalculationState;
  quotation: QuotationDetail | null;
  user?: CurrentUser | null;
  onRecalculate: () => void;
}

/**
 * 用户端报价确认步骤组件
 * 复用管理后台组件，添加用户端样式适配和额外功能
 */
export default function UserQuotationStep({
  state,
  quotation,
  user,
  onRecalculate
}: UserQuotationStepProps) {

  // 同步quotation数据到state.quotation，确保管理后台组件能正确显示
  useEffect(() => {
    if (quotation && quotation.totalCost > 0) {
      // 如果本地quotation有数据且state.quotation没有数据或数据不同步，则更新state
      if (!state.quotation || state.quotation.totalCost !== quotation.totalCost) {
        console.log('同步quotation数据到state:', quotation);
        // 注意：这里我们不能直接修改state，需要通过父组件的更新函数
        // 但由于这是展示组件，我们主要确保按钮的禁用逻辑正确
      }
    }
  }, [quotation, state.quotation]);

  // 获取有效的报价数据（优先使用本地quotation，回退到state.quotation）
  const effectiveQuotation = quotation || state.quotation;
  const hasValidQuotation = effectiveQuotation && effectiveQuotation.totalCost > 0;

  // 保存报价到用户中心
  const handleSaveQuotation = async () => {
    try {
      if (!effectiveQuotation) {
        message.error('暂无报价数据可保存');
        return;
      }

      // 调用保存报价的API
      const { userQuotationApi } = await import('@/services/calculationApi');
      const result = await userQuotationApi.saveQuotation({
        quotation: effectiveQuotation,
        calculationState: state
      });

      if (result.success) {
        message.success('报价已保存到您的用户中心');
      } else {
        message.error(result.error?.message || '保存报价失败');
      }
    } catch (error) {
      console.error('保存报价失败:', error);
      message.error('保存报价失败');
    }
  };

  // 一键复制报价功能
  const handleCopyQuotation = () => {
    if (!hasValidQuotation) {
      message.error('暂无报价数据可复制');
      return;
    }

    const quotationText = generateQuotationText();
    navigator.clipboard.writeText(quotationText).then(() => {
      message.success('报价信息已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 打印报价单功能
  const handlePrintQuotation = async () => {
    if (!hasValidQuotation) {
      message.error('暂无报价数据可打印');
      return;
    }

    try {
      const result = await executePrintQuotation(state);
      if (result.success) {
        message.success(result.message);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('打印失败，请稍后重试');
      console.error('打印错误:', error);
    }
  };

  // 导出PDF功能
  const handleExportPDF = async () => {
    if (!hasValidQuotation) {
      message.error('暂无报价数据可导出');
      return;
    }

    try {
      message.loading('正在生成PDF...', 0);

      // 使用专门的PDF导出工具
      const htmlContent = generateDetailedQuotationHTML(state);
      await exportQuotationToPDFFromHTML(htmlContent, {
        filename: `报价单_${state.basicInfo.name || '未命名项目'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.pdf`,
        format: 'a4',
        orientation: 'portrait',
        quality: 1.5
      });

      message.destroy();
      message.success('PDF导出成功');

    } catch (error) {
      message.destroy();
      message.error('PDF生成失败，请稍后重试');
      console.error('PDF导出错误:', error);
    }
  };

  // 生成报价文本
  const generateQuotationText = (): string => {
    const lines: string[] = [];

    // 项目概要
    lines.push('=== 包装盒报价单 ===');
    lines.push('');
    lines.push('【项目概要】');
    lines.push(`项目名称: ${state.basicInfo.name || '未命名项目'}`);
    lines.push(`生产数量: ${state.basicInfo.quantity.toLocaleString()} 个`);
    lines.push(`总费用: ¥${effectiveQuotation.totalCost.toFixed(2)}`);
    lines.push(`单价: ¥${(effectiveQuotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个`);

    // 费用构成
    lines.push('');
    lines.push('【费用构成】');
    if (effectiveQuotation.materialCost > 0) {
      lines.push(`材料费用: ¥${effectiveQuotation.materialCost.toFixed(2)}`);
    }
    if (effectiveQuotation.processCost > 0) {
      lines.push(`工艺费用: ¥${effectiveQuotation.processCost.toFixed(2)}`);
    }
    if (effectiveQuotation.accessoryCost > 0) {
      lines.push(`配件费用: ¥${effectiveQuotation.accessoryCost.toFixed(2)}`);
    }
    if ((effectiveQuotation.processingFeeCost || 0) > 0) {
      lines.push(`加工费用: ¥${(effectiveQuotation.processingFeeCost || 0).toFixed(2)}`);
    }
    if ((effectiveQuotation.formulaCost || 0) > 0) {
      lines.push(`自定义费用: ¥${(effectiveQuotation.formulaCost || 0).toFixed(2)}`);
    }

    lines.push('');
    lines.push('【联系方式】');
    lines.push('客服电话: 18638728164');
    lines.push('');
    lines.push('注：此报价仅供参考，最终价格以客服确认为准。');

    return lines.join('\n');
  };

  return (
    <div className="user-quotation-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>🎉 报价完成</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            恭喜！您的包装报价已生成完成。您可以保存报价、下载PDF或联系我们的客服进行进一步咨询。
          </p>
        </div>
      </Card>

      {/* 用户端操作按钮 - 响应式设计 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <div style={{ minWidth: '200px' }}>
            <h4 style={{ margin: 0 }}>报价操作</h4>
            <p style={{ margin: '4px 0 0 0', color: '#666' }}>
              选择您需要的操作
            </p>
          </div>
          <Space wrap size="middle">
            <Tooltip title="重新计算所有费用项目的总价">
              <Button
                icon={<ReloadOutlined />}
                onClick={onRecalculate}
                size="middle"
              >
                重新计算
              </Button>
            </Tooltip>

            <Tooltip title="复制详细报价信息到剪贴板">
              <Button
                type="primary"
                icon={<CopyOutlined />}
                onClick={handleCopyQuotation}
                size="middle"
                disabled={!hasValidQuotation}
              >
                一键复制报价
              </Button>
            </Tooltip>

            <Tooltip title="打印当前报价单">
              <Button
                icon={<PrinterOutlined />}
                onClick={handlePrintQuotation}
                size="middle"
                disabled={!hasValidQuotation}
              >
                打印报价单
              </Button>
            </Tooltip>

            <Tooltip title="导出报价单为PDF文件">
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportPDF}
                size="middle"
                disabled={!hasValidQuotation}
              >
                导出PDF
              </Button>
            </Tooltip>

            <Divider type="vertical" />

            <Tooltip title="保存报价到您的用户中心">
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleSaveQuotation}
                size="middle"
                disabled={!hasValidQuotation}
              >
                保存到用户中心
              </Button>
            </Tooltip>
          </Space>
        </div>
      </Card>

      {/* 复用管理后台的报价组件 */}
      <QuotationStep
        state={state}
        onUpdate={{
          formulaConfig: () => {} // 用户端不需要公式配置功能
        }}
        onRecalculate={onRecalculate}
      />

      {/* 用户端额外信息 */}
      <Card style={{ marginTop: 16, backgroundColor: '#fff7e6', border: '1px solid #ffd591' }}>
        <div style={{ color: '#fa8c16' }}>
          <h4 style={{ margin: 0, color: '#fa8c16' }}>📞 需要帮助？</h4>
          <p style={{ margin: '8px 0 0 0', color: '#fa8c16' }}>
            如果您对报价有任何疑问，或需要定制化服务，请联系我们的专业客服团队：
            <br />
            电话：18638728164
            <br />
            我们将为您提供专业的包装解决方案。
          </p>
        </div>
      </Card>
    </div>
  );
}
