import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { printingListParamsSchema, PrintingListParams } from '@/lib/validations/admin/printing';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

/**
 * 用户端获取印刷工艺列表接口
 * 复用管理后台逻辑，用户端只需要登录即可访问
 */
const handler = withValidation<PrintingListParams>(
  printingListParamsSchema,
  async (request: AuthenticatedRequest, validatedQuery: PrintingListParams) => {
    const data = validatedQuery;
    const page = data.page || 1;
    const pageSize = data.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.PrintingWhereInput = {
      isDel: false
    };

    if (data.search) {
      where.machineModel = { contains: data.search };
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.printing.count({ where }),
      prisma.printing.findMany({
        where,
        select: {
          id: true,
          machineModel: true,
          unit: true,
          basePrice: true,
          ctpPlateFee: true,
          spotColorFee: true,
          price1000_1999: true,
          price2000_2999: true,
          price3000_3999: true,
          price4000_4999: true,
          price5000_5999: true,
          price6000_6999: true,
          price7000_7999: true,
          price8000_8999: true,
          price9000_9999: true,
          price10000Plus: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取印刷工艺列表成功'
    );
  }
); 

// 用户端接口：需要登录但不需要管理员权限
export const POST = withAuth(handler, { requireAuth: true });
