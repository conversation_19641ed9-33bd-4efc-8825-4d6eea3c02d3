'use client';

import React from 'react';
import { Card, Statistic, Button, Space, Divider, Typography, Tooltip } from 'antd';
import { CalculatorOutlined, ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { QuotationDetail, BoxBasicInfo } from '@/app/admin/box/calculation/types/calculation';
import { formatCurrency } from '@/app/admin/box/calculation/util';

const { Text } = Typography;

interface UserCalculationSummaryProps {
  quotation: QuotationDetail;
  basicInfo: BoxBasicInfo;
  isCalculating: boolean;
  onRecalculate: () => void;
}

/**
 * 用户端费用汇总组件
 * 简化版本，不包含自动触发重新计算的逻辑
 */
const UserCalculationSummary: React.FC<UserCalculationSummaryProps> = ({
  quotation,
  basicInfo,
  isCalculating,
  onRecalculate
}) => {
  // 获取当前数量
  const currentQuantity = basicInfo?.quantity || 0;

  return (
    <Card
      title={
        <Space>
          <CalculatorOutlined />
          <span>费用汇总</span>
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={onRecalculate}
          loading={isCalculating}
          size="small"
        >
          重算
        </Button>
      }
      className="shadow-lg"
    >
      {/* 基础信息 */}
      <div>
        <Text type="secondary" className="text-xs">数量</Text>
        <div className="font-medium text-sm">{currentQuantity} 个</div>
      </div>

      <Divider className="my-2" />

      {/* 费用明细 */}
      <Space direction="vertical" className="w-full" size="small">
        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="包含面纸、灰板等材料费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>材料费用</Text>
            <Text>{formatCurrency(quotation.materialCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="包含印刷、覆膜、丝印、烫金、凹凸、模切等工艺费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>工艺费用</Text>
            <Text>{formatCurrency(quotation.processCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="包含常规配件和礼盒配件费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>配件费用</Text>
            <Text>{formatCurrency(quotation.accessoryCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="加工费用和自定义费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>其他费用</Text>
            <Text>{formatCurrency((quotation.processingFeeCost || 0) + (quotation.formulaCost || 0))}</Text>
          </Space>
        </div>
      </Space>

      <Divider className="my-2" />

      {/* 总价 */}
      <div className="bg-blue-50 p-3 rounded-lg">
        <Statistic
          title="总费用"
          value={quotation.totalCost}
          formatter={(value) => formatCurrency(Number(value))}
          valueStyle={{ color: '#1890ff', fontSize: '18px', fontWeight: 'bold' }}
        />
      </div>

      {/* 单价 */}
      {currentQuantity > 0 && (
        <div className="bg-gray-50 p-2 rounded text-center mt-2">
          <Text type="secondary" className="text-xs">单价</Text>
          <div className="text-sm font-semibold text-gray-700">
            {formatCurrency(quotation.totalCost / currentQuantity)}
          </div>
        </div>
      )}

      {/* 用户端提示 */}
      <div className="mt-3 p-2 bg-orange-50 rounded text-center">
        <Text type="secondary" className="text-xs">
          💡 价格仅供参考，最终报价以客服确认为准
        </Text>
      </div>
    </Card>
  );
};

export default UserCalculationSummary;
