'use client';

import React, { Component, ReactNode } from 'react';
import { <PERSON><PERSON>, Result, Card, Typography, Space, Collapse } from 'antd';
import { ReloadOutlined, BugOutlined, HomeOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

// 错误边界状态
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: { componentStack: string } | null;
  errorId: string;
}

// 错误边界属性
interface ErrorBoundaryProps {
  children: ReactNode;
  /** 自定义错误回退UI */
  fallback?: (error: Error, errorInfo: any, resetError: () => void) => ReactNode;
  /** 错误回调函数 */
  onError?: (error: Error, errorInfo: any) => void;
  /** 是否显示错误详情 */
  showErrorDetails?: boolean;
  /** 是否在开发环境显示完整错误信息 */
  showFullErrorInDev?: boolean;
  /** 自定义重置后的跳转路径 */
  resetPath?: string;
  /** 错误边界的名称，用于日志记录 */
  boundaryName?: string;
}

/**
 * 生成错误ID
 */
function generateErrorId(): string {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 记录错误到控制台和外部服务
 */
function logError(
  error: Error,
  errorInfo: any,
  errorId: string,
  boundaryName?: string
) {
  const logData = {
    errorId,
    boundaryName: boundaryName || 'Unknown',
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    errorInfo,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  console.group(`🚨 错误边界捕获到错误 [${errorId}]`);
  console.error('错误详情:', error);
  console.error('组件堆栈:', errorInfo.componentStack);
  console.error('完整日志:', logData);
  console.groupEnd();

  // 在生产环境中，这里应该发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 示例：发送到错误监控服务
    // errorReportingService.report(logData);
  }
}

/**
 * 默认错误UI组件
 */
interface DefaultErrorUIProps {
  error: Error;
  errorInfo: any;
  errorId: string;
  resetError: () => void;
  showErrorDetails: boolean;
  showFullErrorInDev: boolean;
  resetPath?: string;
}

function DefaultErrorUI({
  error,
  errorInfo,
  errorId,
  resetError,
  showErrorDetails,
  showFullErrorInDev,
  resetPath
}: DefaultErrorUIProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const handleGoHome = () => {
    if (resetPath) {
      window.location.href = resetPath;
    } else {
      window.location.href = '/';
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div style={{ padding: '24px', minHeight: '400px' }}>
      <Result
        status="error"
        title="应用出现错误"
        subTitle={`抱歉，应用遇到了一个意外错误。错误ID: ${errorId}`}
        extra={
          <Space>
            <Button type="primary" icon={<ReloadOutlined />} onClick={resetError}>
              重新尝试
            </Button>
            <Button icon={<HomeOutlined />} onClick={handleGoHome}>
              返回首页
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReload}>
              刷新页面
            </Button>
          </Space>
        }
      />
      
      {(showErrorDetails || (isDevelopment && showFullErrorInDev)) && (
        <Card
          title={
            <Space>
              <BugOutlined />
              <span>错误详情</span>
            </Space>
          }
          style={{ marginTop: '24px' }}
          size="small"
        >
          <Collapse
            size="small"
            ghost
            items={[
              {
                key: 'error',
                label: '错误信息',
                children: (
                  <div style={{ fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
                    <Text strong>错误类型: </Text>
                    <Text code>{error.name}</Text>
                    <br />
                    <Text strong>错误消息: </Text>
                    <Text code>{error.message}</Text>
                  </div>
                )
              },
              ...(isDevelopment && showFullErrorInDev ? [
                {
                  key: 'stack',
                  label: '错误堆栈',
                  children: (
                    <Paragraph>
                      <pre style={{
                        fontSize: '12px',
                        backgroundColor: '#f5f5f5',
                        padding: '12px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        whiteSpace: 'pre-wrap',
                      }}>
                        {error.stack}
                      </pre>
                    </Paragraph>
                  )
                },
                {
                  key: 'componentStack',
                  label: '组件堆栈',
                  children: (
                    <Paragraph>
                      <pre style={{
                        fontSize: '12px',
                        backgroundColor: '#f5f5f5',
                        padding: '12px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        whiteSpace: 'pre-wrap',
                      }}>
                        {errorInfo.componentStack}
                      </pre>
                    </Paragraph>
                  )
                }
              ] : [])
            ]}
          />
        </Card>
      )}
    </div>
  );
}

/**
 * 错误边界组件
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: generateErrorId(),
    };
  }

  componentDidCatch(error: Error, errorInfo: { componentStack: string }) {
    const { onError, boundaryName } = this.props;
    
    // 更新状态
    this.setState({
      errorInfo,
    });

    // 记录错误
    logError(error, errorInfo, this.state.errorId, boundaryName);

    // 调用错误回调
    if (onError) {
      onError(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  render() {
    const {
      children,
      fallback,
      showErrorDetails = false,
      showFullErrorInDev = true,
      resetPath
    } = this.props;
    
    const { hasError, error, errorInfo, errorId } = this.state;

    if (hasError && error) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback(error, errorInfo, this.resetError);
      }

      // 否则使用默认错误UI
      return (
        <DefaultErrorUI
          error={error}
          errorInfo={errorInfo}
          errorId={errorId}
          resetError={this.resetError}
          showErrorDetails={showErrorDetails}
          showFullErrorInDev={showFullErrorInDev}
          resetPath={resetPath}
        />
      );
    }

    return children;
  }
}

/**
 * 函数式错误边界HOC
 */
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WithErrorBoundaryComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = 
    `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
}

/**
 * 简化的错误边界组件，适用于页面级别
 */
export function PageErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      boundaryName="PageErrorBoundary"
      showErrorDetails={process.env.NODE_ENV === 'development'}
      showFullErrorInDev={true}
      resetPath="/"
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * 适用于组件级别的错误边界
 */
export function ComponentErrorBoundary({ 
  children, 
  componentName 
}: { 
  children: ReactNode;
  componentName?: string;
}) {
  return (
    <ErrorBoundary
      boundaryName={`ComponentErrorBoundary-${componentName || 'Unknown'}`}
      showErrorDetails={false}
      showFullErrorInDev={true}
      fallback={(error, errorInfo, resetError) => (
        <div style={{ 
          padding: '16px', 
          border: '1px dashed #ff4d4f', 
          borderRadius: '4px',
          backgroundColor: '#fff2f0',
          textAlign: 'center'
        }}>
          <Text type="danger">组件加载失败</Text>
          <br />
          <Button 
            size="small" 
            type="link" 
            onClick={resetError}
            style={{ padding: 0, marginTop: '8px' }}
          >
            重新加载
          </Button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}

export default ErrorBoundary; 