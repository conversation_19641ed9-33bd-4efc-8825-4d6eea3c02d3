'use client';

import React from 'react';
import { Card } from 'antd';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';

// 直接复用管理后台的工艺步骤组件
import ProcessStep from '@/app/admin/box/calculation/components/steps/ProcessStep';

interface UserProcessStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
}

/**
 * 用户端工艺选择步骤组件
 * 复用管理后台组件，添加用户端样式适配
 */
export default function UserProcessStep({
  state,
  onUpdate
}: UserProcessStepProps) {
  return (
    <div className="user-process-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>💡 操作提示</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            请根据您的包装需求选择相应的印刷和后道工艺。不同工艺会影响最终的价格和效果。
          </p>
        </div>
      </Card>

      {/* 复用管理后台的工艺组件 */}
      <ProcessStep
        state={state}
        onUpdate={onUpdate}
        onRecalculate={() => {}} // 空函数，用户端不需要手动触发重新计算
      />
    </div>
  );
}
