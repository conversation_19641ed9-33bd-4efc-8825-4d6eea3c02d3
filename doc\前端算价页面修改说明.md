# 前端算价页面修改说明

## 修改概述

根据用户要求，对 `src/app/(front)/calculation/page.tsx` 页面进行了以下重要修改：

### 1. 页面导航逻辑修改

#### Quote 页面跳转逻辑
- **文件**: `src/app/(front)/quote/page.tsx`
- **修改内容**: 
  - 修改 `handleBoxClick` 函数，实现点击盒型卡片后跳转到算价页面
  - 通过 URL 参数 `boxId` 传递选中的盒型ID
  - 跳转路径: `/calculation?boxId=${box.id}`

#### Calculation 页面接收参数
- **文件**: `src/app/(front)/calculation/page.tsx`
- **修改内容**:
  - 保持原有的 URL 参数解析逻辑
  - 通过 `useEffect` 监听 `boxId` 参数并加载盒型数据
  - 将加载的盒型数据传递给 `UserCalculationWizard` 组件

### 2. 页面布局设计修改

#### 左右布局结构
- **文件**: `src/app/(front)/calculation/components/UserCalculationWizard.tsx`
- **修改内容**:
  - 采用 Ant Design 的 `Row` 和 `Col` 组件实现左右布局
  - 左侧主要内容区域占 18 列（除报价确认步骤外）
  - 右侧费用汇总区域占 6 列
  - 报价确认步骤时占满 24 列

#### 集成 CalculationSummary 组件
- **导入组件**: 从管理后台导入 `CalculationSummary` 组件
- **布局位置**: 放置在右侧 6 列区域
- **功能特性**:
  - 实时显示计算结果和成本明细
  - 支持重新计算功能
  - 显示材料费用详情悬浮提示
  - 在报价确认步骤时隐藏

### 3. 移除盒型选择功能

#### UserBasicInfoStep 组件修改
- **文件**: `src/app/(front)/calculation/components/steps/UserBasicInfoStep.tsx`
- **修改内容**:
  - 移除 `UserBoxSelector` 组件的使用
  - 移除盒型选择相关的状态和处理函数
  - 添加自动初始化逻辑，当传入 `sourceBox` 时自动填充基础信息
  - 显示当前选择的盒型信息卡片
  - 当没有盒型时显示警告提示

#### 自动初始化逻辑
- 使用 `useEffect` 监听 `sourceBox` 变化
- 自动生成计算名称（格式：`盒型名_计算_时间`）
- 自动填充盒型的动态属性和部件信息
- 设置默认数量为 1000

### 4. 用户体验优化

#### 无盒型状态处理
- **文件**: `src/app/(front)/calculation/components/UserCalculationWizard.tsx`
- **功能**: 当没有传递盒型参数时，显示友好的提示页面
- **内容**: 包含返回选择盒型的按钮，引导用户回到 quote 页面

#### 步骤描述更新
- 将第一步描述从"选择盒型和填写基本参数"改为"确认盒型信息和填写基本参数"
- 更准确地反映了新的页面流程

#### 导航按钮布局
- 将导航按钮移至右侧费用汇总下方
- 保持与管理后台一致的布局风格
- 包含"重置"、"上一步"、"下一步"按钮

## 技术实现细节

### 组件复用策略
- 最大化复用管理后台的计算相关组件
- 保持 UI 一致性和功能统一性
- 通过参数传递实现不同场景的适配

### 布局响应式设计
- 使用 Ant Design 的栅格系统
- 确保在不同屏幕尺寸下的良好显示效果
- 保持与管理后台相同的设计模式

### 状态管理
- 复用管理后台的 `useCalculationState` Hook
- 保持计算状态的一致性和可靠性
- 支持实时计算和状态同步

## 测试建议

1. **页面跳转测试**:
   - 从 quote 页面点击盒型卡片
   - 验证是否正确跳转到算价页面并传递参数

2. **布局显示测试**:
   - 验证左右布局是否正确显示
   - 检查费用汇总组件是否正常工作

3. **盒型信息测试**:
   - 验证盒型信息是否正确显示和初始化
   - 测试无盒型时的提示页面

4. **计算功能测试**:
   - 验证各步骤的计算功能是否正常
   - 检查实时费用汇总是否准确

## 注意事项

- 确保数据库中有已发布的盒型数据用于测试
- 验证所有 API 接口的正常工作
- 检查用户权限和认证功能
- 测试不同浏览器的兼容性
