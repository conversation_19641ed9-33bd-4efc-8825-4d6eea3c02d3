import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { accessoryQuerySchema, AccessoryQueryParams } from '@/lib/validations/admin/accessory';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { Prisma } from '@prisma/client';

/**
 * 用户端获取配件列表接口
 * 复用管理后台逻辑，用户端只需要登录即可访问
 */
const handler = withValidation<AccessoryQueryParams>(
  accessoryQuerySchema,
  async (request: AuthenticatedRequest, validatedQuery: AccessoryQueryParams) => {
    const data = validatedQuery;
    const page = data.page || 1;
    const pageSize = data.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: Prisma.AccessoryWhereInput = {
      isDel: false
    };

    if (data.keyword) {
      where.name = { contains: data.keyword };
    }

    // 查询总数和列表数据
    const [total, list] = await Promise.all([
      prisma.accessory.count({ where }),
      prisma.accessory.findMany({
        where,
        select: {
          id: true,
          name: true,
          unit: true,
          price: true,
          initialPrice: true,
          weight: true,
          remark: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      })
    ]);

    return paginatedResponse(
      list,
      {
        page,
        pageSize,
        total,
      },
      '获取配件列表成功'
    );
  }
); 

// 用户端接口：需要登录但不需要管理员权限
export const POST = withAuth(handler, { requireAuth: true });
