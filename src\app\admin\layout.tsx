'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, theme, But<PERSON>, Drawer, MenuProps, Dropdown, Avatar, Space, message } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  InboxOutlined,
  SettingOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  FileImageOutlined,
  ScissorOutlined,
  FormOutlined,
  ToolOutlined,
  AppstoreOutlined,
  GiftOutlined,
  PrinterOutlined,
  BgColorsOutlined,
  BorderOutlined,
  HighlightOutlined,
  StarOutlined,
  CompressOutlined,
  BorderInnerOutlined,
  CalculatorOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { AdminAuthGuard } from '@/components';
import { useAuth } from '@/hooks/useAuth';
import { logout } from '@/services/authApi';
import { getUserRoleLabel } from '@/lib/auth/client';

const { Header, Sider, Content } = Layout;

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, clearUser } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 处理用户登出
  const handleLogout = async () => {
    try {
      await logout();
      clearUser();
      message.success('登出成功');
      router.push('/login');
    } catch (error) {
      console.error('登出失败:', error);
      message.error('登出失败，请重试');
    }
  };



  // 检测窗口大小并设置移动端状态
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 处理菜单收缩/展开
  const toggleCollapsed = () => {
    if (isMobile) {
      setDrawerVisible(!drawerVisible);
    } else {
      setCollapsed(!collapsed);
    }
  };

  // 根据当前路径获取选中的菜单键
  const getSelectedKeys = () => {
    if (pathname === '/admin/dashboard') return ['1'];

    // 盒型管理
    if (pathname.startsWith('/admin/box')) return ['2'];

    // 自定义公式
    if (pathname.startsWith('/admin/customFormula')) return ['3'];

    // 材料数据库
    if (pathname.startsWith('/admin/material/paper')) return ['4-1'];
    if (pathname.startsWith('/admin/material/specialPaper')) return ['4-2'];
    if (pathname.startsWith('/admin/material/greyBoard')) return ['4-3'];
    if (pathname.startsWith('/admin/material/sticker')) return ['4-4'];
    if (pathname.startsWith('/admin/material/specialMaterial')) return ['4-5'];
    if (pathname.startsWith('/admin/material/accessory')) return ['4-6'];
    if (pathname.startsWith('/admin/material/boxMaterial')) return ['4-7'];
    if (pathname.startsWith('/admin/material/materialSize')) return ['4-8'];

    // 工艺工资
    if (pathname.startsWith('/admin/craftSalary/printing')) return ['5-1'];
    if (pathname.startsWith('/admin/craftSalary/surfaceProcess')) return ['5-2'];
    if (pathname.startsWith('/admin/craftSalary/silkScreenProcess')) return ['5-3'];
    if (pathname.startsWith('/admin/craftSalary/corrugatedProcess')) return ['5-4'];
    if (pathname.startsWith('/admin/craftSalary/laminatingProcess')) return ['5-5'];
    if (pathname.startsWith('/admin/craftSalary/hotStampingProcess')) return ['5-6'];
    if (pathname.startsWith('/admin/craftSalary/embossingProcess')) return ['5-7'];
    if (pathname.startsWith('/admin/craftSalary/dieCuttingProcess')) return ['5-8'];
    if (pathname.startsWith('/admin/craftSalary/processingFee')) return ['5-9'];

    // 系统设置
    if (pathname.startsWith('/admin/settings')) return ['6'];

    return [];
  };

  // 获取当前展开的子菜单
  const getOpenKeys = () => {
    const openKeys = [];

    // 材料数据库子菜单
    if (pathname.startsWith('/admin/material/')) {
      openKeys.push('4');
    }

    // 工艺工资子菜单
    if (pathname.startsWith('/admin/craftSalary/')) {
      openKeys.push('5');
    }

    return openKeys;
  };

  // 菜单项定义
  const menuItems: MenuProps['items'] = [
    {
      key: '1',
      icon: <DashboardOutlined />,
      label: <Link href="/admin/dashboard">仪表盘</Link>,
    },
    {
      key: '2',
      icon: <InboxOutlined />,
      label: <Link href="/admin/box">盒型管理</Link>,
    },
    {
      key: '3',
      icon: <FormOutlined />,
      label: <Link href="/admin/customFormula">自定义公式</Link>,
    },
    {
      key: '4',
      icon: <DatabaseOutlined />,
      label: '材料数据库',
      children: [
        {
          key: '4-1',
          icon: <FileTextOutlined />,
          label: <Link href="/admin/material/paper">纸类</Link>,
        },
        {
          key: '4-2',
          icon: <FileImageOutlined />,
          label: <Link href="/admin/material/specialPaper">特种纸</Link>,
        },
        {
          key: '4-3',
          icon: <AppstoreOutlined />,
          label: <Link href="/admin/material/greyBoard">灰板纸密度板</Link>,
        },
        // {
        //   key: '4-4',
        //   icon: <ScissorOutlined />,
        //   label: <Link href="/admin/material/sticker">不干胶</Link>,
        // },
        // {
        //   key: '4-5',
        //   icon: <ToolOutlined />,
        //   label: <Link href="/admin/material/specialMaterial">特殊材料</Link>,
        // },
        {
          key: '4-6',
          icon: <GiftOutlined />,
          label: <Link href="/admin/material/accessory">配件</Link>,
        },
        {
          key: '4-7',
          icon: <InboxOutlined />,
          label: <Link href="/admin/material/boxMaterial">纸箱材料</Link>,
        },
        {
          key: '4-8',
          icon: <SettingOutlined />,
          label: <Link href="/admin/material/materialSize">材料尺寸</Link>,
        },
      ],
    },
    {
      key: '5',
      icon: <ToolOutlined />,
      label: '工艺工资',
      children: [
        {
          key: '5-1',
          icon: <PrinterOutlined />,
          label: '印刷工艺',
          onClick: () => router.push('/admin/craftSalary/printing'),
        },
        {
          key: '5-2',
          icon: <HighlightOutlined />,
          label: '覆膜工艺',
          onClick: () => router.push('/admin/craftSalary/surfaceProcess'),
        },
        {
          key: '5-3',
          icon: <BgColorsOutlined />,
          label: '丝印工艺',
          onClick: () => router.push('/admin/craftSalary/silkScreenProcess'),
        },
        {
          key: '5-4',
          icon: <BorderOutlined />,
          label: '瓦楞工艺',
          onClick: () => router.push('/admin/craftSalary/corrugatedProcess'),
        },
        {
          key: '5-5',
          icon: <BorderInnerOutlined />,
          label: '对裱工艺',
          onClick: () => router.push('/admin/craftSalary/laminatingProcess'),
        },
        {
          key: '5-6',
          icon: <StarOutlined />,
          label: '烫金工艺',
          onClick: () => router.push('/admin/craftSalary/hotStampingProcess'),
        },
        {
          key: '5-7',
          icon: <CompressOutlined />,
          label: '凹凸工艺',
          onClick: () => router.push('/admin/craftSalary/embossingProcess'),
        },
        {
          key: '5-8',
          icon: <ScissorOutlined />,
          label: '模切工艺',
          onClick: () => router.push('/admin/craftSalary/dieCuttingProcess'),
        },
        {
          key: '5-9',
          icon: <CalculatorOutlined />,
          label: '加工费',
          onClick: () => router.push('/admin/craftSalary/processingFee'),
        },
      ],
    },
    {
      key: '6',
      icon: <SettingOutlined />,
      label: <Link href="/admin/settings">系统设置</Link>,
    },
  ];

  // 侧边菜单内容
  const sideMenuContent = (
    <>
      <div className="logo" style={{ height: 32, margin: 16, color: '#fff', fontSize: collapsed && !isMobile ? 16 : 18, fontWeight: 'bold', textAlign: 'center', overflow: 'hidden', whiteSpace: 'nowrap' }}>
        {collapsed && !isMobile ? 'YCBZ' : '盒型管理系统'}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={menuItems}
        onClick={() => isMobile && setDrawerVisible(false)}
      />
    </>
  );

  return (
    <Layout hasSider style={{ minHeight: '100vh', width: '100%' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          theme="dark"
          width={200}
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
          }}
        >
          {sideMenuContent}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          placement="left"
          closable={false}
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={200}
          styles={{ body: { padding: 0, background: '#001529' } }}
        >
          {sideMenuContent}
        </Drawer>
      )}

      <Layout style={{ marginLeft: isMobile ? 0 : (collapsed ? 80 : 200), transition: 'margin-left 0.2s' }}>
        <Header
          style={{
            padding: '0 16px',
            background: colorBgContainer,
            boxShadow: '0 1px 4px rgba(0, 21, 41, 0.08)',
            position: 'sticky',
            top: 0,
            zIndex: 1,
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={isMobile ? <MenuUnfoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
              onClick={toggleCollapsed}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
            <span style={{ fontSize: '18px', fontWeight: 'bold', marginLeft: 8 }}>管理后台</span>
          </div>

          {/* 用户信息和操作区域 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {user && (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'userInfo',
                      label: (
                        <div style={{ padding: '8px 0' }}>
                          <div style={{ fontWeight: 'bold' }}>{user.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {getUserRoleLabel(user.role)}
                          </div>
                          <div style={{ fontSize: '12px', color: '#999' }}>
                            {user.phone}
                          </div>
                        </div>
                      ),
                      disabled: true
                    },
                    {
                      type: 'divider'
                    },
                    {
                      key: 'logout',
                      label: (
                        <Space>
                          <LogoutOutlined />
                          退出登录
                        </Space>
                      ),
                      onClick: handleLogout
                    }
                  ]
                }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Button type="text" style={{ height: 64, padding: '0 12px' }}>
                  <Space>
                    <Avatar
                      size="small"
                      icon={<UserOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    />
                    <span style={{ display: isMobile ? 'none' : 'inline' }}>
                      {user.name}
                    </span>
                  </Space>
                </Button>
              </Dropdown>
            )}
          </div>
        </Header>
        <Content
          style={{
            margin: isMobile ? '8px' : '16px',
            padding: isMobile ? 16 : 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
            overflowX: 'hidden'
          }}
        >
          <AdminAuthGuard>
            {children}
          </AdminAuthGuard>
        </Content>
      </Layout>
    </Layout>
  );
}