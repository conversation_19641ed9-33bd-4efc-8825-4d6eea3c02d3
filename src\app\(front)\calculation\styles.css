/* 用户端算价页面样式 */

.user-calculation-wizard {
  max-width: 100%;
  margin: 0 auto;
}

/* 步骤导航响应式 */
.user-calculation-wizard .ant-steps {
  overflow-x: auto;
  padding-bottom: 8px;
}

.user-calculation-wizard .ant-steps-item {
  min-width: 120px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-calculation-wizard .ant-steps {
    flex-direction: column;
  }
  
  .user-calculation-wizard .ant-steps-item {
    min-width: auto;
    width: 100%;
    margin-bottom: 16px;
  }
  
  .user-calculation-wizard .ant-steps-item-content {
    min-height: auto;
  }
  
  .user-calculation-wizard .ant-card {
    margin-left: -8px;
    margin-right: -8px;
  }
}

/* 盒型选择器样式 */
.user-box-selector .ant-select-dropdown {
  max-height: 400px;
}

.user-box-selector .ant-select-item {
  padding: 12px;
}

.user-box-selector .ant-select-item-option-content {
  display: block;
}

/* 提示卡片样式 */
.user-tip-card {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border: 1px solid #b7eb8f;
  border-radius: 8px;
}

.user-tip-card h4 {
  color: #52c41a !important;
  font-weight: 600;
}

.user-tip-card p {
  color: #52c41a !important;
  margin: 8px 0 0 0;
}

/* 操作按钮区域 */
.user-action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

@media (max-width: 576px) {
  .user-action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .user-action-buttons .ant-space {
    justify-content: center;
  }
}

/* 报价结果样式 */
.quotation-result {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quotation-summary {
  background: linear-gradient(135deg, #fff7e6 0%, #f6ffed 100%);
  border: 1px solid #ffd591;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.quotation-summary .total-price {
  font-size: 24px;
  font-weight: bold;
  color: #fa8c16;
}

/* 成本明细表格 */
.cost-detail-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.cost-detail-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 加载状态 */
.calculation-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fafafa;
  border-radius: 8px;
}

.calculation-loading .ant-spin {
  margin-bottom: 16px;
}

/* 打印样式 */
@media print {
  .user-calculation-wizard .ant-card {
    box-shadow: none;
    border: 1px solid #d9d9d9;
  }
  
  .user-action-buttons,
  .user-tip-card {
    display: none !important;
  }
  
  .quotation-result {
    page-break-inside: avoid;
  }
  
  .cost-detail-table {
    font-size: 12px;
  }
  
  .quotation-summary {
    background: #f5f5f5 !important;
    border: 1px solid #d9d9d9 !important;
  }
}

/* 动画效果 */
.user-calculation-wizard .ant-card {
  transition: all 0.3s ease;
}

.user-calculation-wizard .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-calculation-wizard .ant-btn {
  transition: all 0.3s ease;
}

.user-calculation-wizard .ant-btn:hover {
  transform: translateY(-1px);
}

/* 错误状态 */
.calculation-error {
  text-align: center;
  padding: 40px 20px;
  color: #ff4d4f;
}

.calculation-error .ant-result-icon {
  margin-bottom: 16px;
}

/* 成功状态 */
.calculation-success {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border-radius: 8px;
  margin-bottom: 16px;
}

.calculation-success .success-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 16px;
}

/* 自定义滚动条 */
.user-calculation-wizard ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.user-calculation-wizard ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.user-calculation-wizard ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.user-calculation-wizard ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
