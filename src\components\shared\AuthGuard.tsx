'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Spin, message } from 'antd';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
  showMessage?: boolean;
  messageText?: string;
}

/**
 * 用户认证保护组件
 * 用于保护需要登录的页面和组件
 */
export default function AuthGuard({
  children,
  redirectTo = '/login',
  requireAuth = true,
  showMessage = true,
  messageText = '请先登录后再访问此页面'
}: AuthGuardProps) {
  const router = useRouter();
  const { user, loading, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!loading && requireAuth) {
      if (!isAuthenticated) {
        if (showMessage) {
          message.warning(messageText);
        }
        
        // 构建重定向URL，包含当前页面作为回调
        const currentPath = window.location.pathname + window.location.search;
        const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
        router.push(redirectUrl);
      }
    }
  }, [loading, isAuthenticated, requireAuth, router, redirectTo, showMessage, messageText]);

  // 如果正在加载认证状态，显示加载器
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '200px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果需要认证但用户未登录，不渲染内容
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // 认证通过或不需要认证，渲染子组件
  return <>{children}</>;
}
