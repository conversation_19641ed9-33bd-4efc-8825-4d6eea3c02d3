import React from 'react';

interface TopNotificationProps {
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
}

const bgColorMap = {
  info: 'bg-blue-500',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500',
};

export default function TopNotification({ message, type = 'info' }: TopNotificationProps) {
  return (
    <div className={`w-full text-white p-2 text-center ${bgColorMap[type]}`}>
      {message}
    </div>
  );
}