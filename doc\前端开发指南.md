# 前端开发指南

## 技术栈概览

### 核心技术栈

| 技术 | 版本 | 用途 | 文档链接 |
|------|------|------|----------|
| **Next.js** | ^13.5.11 | React 全栈框架 (App Router) | [官方文档](https://nextjs.org/docs) |
| **React** | 18.2.0 | UI 组件库 | [官方文档](https://react.dev/) |
| **TypeScript** | ^5 | 类型安全 | [官方文档](https://www.typescriptlang.org/) |
| **Ant Design** | ^5.25.1 | UI 组件库 | [官方文档](https://ant.design/) |
| **Tailwind CSS** | ^4 | 原子化 CSS 框架 | [官方文档](https://tailwindcss.com/) |

### 专用业务依赖

| 依赖 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **math.js** | ^14.5.0 | 公式计算引擎 | 印刷报价公式计算核心 |
| **@dnd-kit/core** | ^6.3.1 | 拖拽功能 | 盒型部件、公式排序等交互 |
| **@dnd-kit/sortable** | ^10.0.0 | 可排序拖拽 | 列表项拖拽排序 |
| **@dnd-kit/utilities** | ^3.2.2 | 拖拽工具函数 | 拖拽功能辅助函数 |
| **pinyin-pro** | ^3.26.0 | 拼音处理 | 中文搜索和排序功能 |

### 表单和数据处理

| 依赖 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **React Hook Form** | ^7.56.4 | 表单管理 | 高性能表单处理 |
| **@hookform/resolvers** | ^5.0.1 | 表单校验 | 与 Zod 集成的表单校验 |
| **Zod** | ^3.24.4 | 参数校验 | 前后端统一的数据校验 |

### HTTP 和状态管理

| 依赖 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Axios** | ^1.9.0 | HTTP 客户端 | API 请求处理 |
| **react-hot-toast** | ^2.5.2 | 消息提示 | 用户操作反馈 |
| **dayjs** | ^1.11.13 | 日期处理 | 日期格式化和计算 |

### 图标和UI增强

| 依赖 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **@ant-design/icons** | ^6.0.0 | 图标库 | Ant Design 官方图标 |
| **@heroicons/react** | ^2.2.0 | 图标库 | Heroicons React 组件 |

### 开发工具链

| 工具 | 版本 | 用途 | 配置文件 |
|------|------|------|----------|
| **ESLint** | ^9 | 代码质量检查 | `eslint.config.mjs` |
| **TypeScript** | ^5 | 类型检查 | `tsconfig.json` |
| **Tailwind CSS** | ^4 | 样式处理 | `tailwind.config.js` |
| **PostCSS** | ^4 | CSS 处理 | `postcss.config.mjs` |

## 项目结构规范

### 源代码目录结构

```
src/
├── app/                        # Next.js App Router 根目录
│   ├── admin/                  # 管理后台页面
│   │   ├── box/                # 盒型管理模块
│   │   │   ├── page.tsx        # 盒型列表页面
│   │   │   ├── create/         # 创建盒型页面
│   │   │   ├── calculation/    # 盒型计算模块
│   │   │   │   ├── page.tsx    # 盒型计算主页面
│   │   │   │   ├── components/ # 计算相关组件
│   │   │   │   ├── hooks/      # 计算状态管理Hooks
│   │   │   │   ├── types/      # 计算相关类型定义
│   │   │   │   ├── utils/      # 计算引擎和工具
│   │   │   │   └── validations/ # 计算参数校验
│   │   │   └── [id]/           # 盒型详情和编辑页面
│   │   ├── material/           # 材料管理模块
│   │   │   └── xxxxx/page      # 材料子模块
│   │   ├── customFormula/      # 自定义公式模块
│   │   │   ├── page.tsx        # 公式列表页面
│   │   │   ├── create/         # 创建公式页面
│   │   │   └── [id]/           # 公式详情和编辑页面
│   │   ├── craftSalary/        # 工艺工价模块
│   │   │   └── xxxx/page       # 工艺工价子模块
│   │   ├── settings/           # 系统设置模块
│   │   │   └── page.tsx        # 设置页面
│   │   ├── dashboard/          # 仪表板模块
│   │   │   └── page.tsx        # 仪表板页面
│   │   ├── layout.tsx          # 后台布局组件
│   │   ├── page.tsx            # 后台首页
│   │   └── metadata.tsx        # 后台元数据
│   ├── api/v1/                 # API 接口路由
│   │   └── admin/              # 管理后台接口
│   │       ├── box/           # 盒型相关接口
│   │       │   ├── getList/   # 获取盒型列表
│   │       │   ├── getDetail/ # 获取盒型详情
│   │       │   ├── create/    # 创建盒型
│   │       │   ├── update/    # 更新盒型
│   │       │   └── delete/    # 删除盒型
│   │       ├── material/      # 材料相关接口
│   │       ├── customFormula/ # 自定义公式接口
│   │       └── craftSalary/   # 工艺工价接口
│   ├── globals.css             # 全局样式文件
│   ├── layout.tsx              # 根布局组件
│   ├── page.tsx                # 应用首页
│   └── providers.tsx           # 全局状态提供者
├── components/                  # 组件目录
│   ├── admin/                  # 管理后台组件
│   │   ├── AdminAuthGuard.tsx # 权限检查组件
│   │   ├── AccessDenied.tsx   # 权限不足提示组件
│   │   ├── FrontBoxConfig.tsx # 前台盒型配置组件
│   │   ├── ProfitTaxConfig.tsx # 利润税率配置组件
│   │   └── index.ts           # 管理后台组件导出
│   ├── front/                  # 前端页面组件
│   │   ├── Header.tsx         # 页面头部组件
│   │   ├── Footer.tsx         # 页面底部组件
│   │   ├── Hero.tsx           # 首页英雄区组件
│   │   ├── BoxShowcase.tsx    # 盒型展示组件
│   │   ├── CustomBoxCard.tsx  # 自定义盒型卡片组件
│   │   ├── Interval.tsx       # 间隔组件
│   │   └── index.ts           # 前端组件导出
│   ├── shared/                 # 共享组件
│   │   ├── Breadcrumb.tsx     # 面包屑导航组件
│   │   ├── ErrorBoundary.tsx  # 错误边界组件
│   │   ├── TopNotification.tsx # 顶部通知组件
│   │   └── index.ts           # 共享组件导出
│   ├── ui/                     # UI 基础组件
│   │   └── index.ts           # UI 组件导出
│   └── index.ts               # 组件统一导出入口
├── lib/                         # 公用库目录
│   ├── hooks/                  # 自定义 Hooks
│   │   ├── useErrorHandler.ts # 错误处理 Hook
│   │   └── useAsyncError.ts   # 异步错误处理 Hook
│   ├── utils/                  # 工具函数
│   │   ├── handler.ts         # 通用处理函数
│   │   ├── request.ts         # 请求工具函数
│   │   └── apiResponse.ts     # API 响应工具
│   ├── validations/            # Zod 校验规则
│   │   └── admin/             # 管理后台校验规则
│   │       ├── box.ts         # 盒型校验规则
│   │       ├── material.ts    # 材料校验规则
│   │       ├── customFormula.ts # 公式校验规则
│   │       └── craftSalary.ts # 工价校验规则
│   ├── middleware/             # 中间件
│   │   └── errorHandler.ts   # 错误处理中间件
│   ├── constants/              # 常量定义
│   │   ├── api.ts             # API 相关常量
│   │   └── business.ts        # 业务相关常量
│   └── prisma.ts              # Prisma 客户端配置
├── services/                    # 前端 API 服务层
│   └── adminApi.ts             # 管理后台 API 服务
└── types/                      # TypeScript 类型定义
    ├── common.ts               # 通用类型定义
    ├── box.ts                  # 盒型相关类型
    ├── material.ts             # 材料相关类型
    ├── customFormula.ts        # 自定义公式类型
    └── craftSalary.ts          # 工艺工价类型
```

### 文件命名规范

| 文件类型 | 命名规范 | 示例 | 说明 |
|----------|----------|------|------|
| **页面组件** | `page.tsx` | `page.tsx` | Next.js App Router 页面文件 |
| **布局组件** | `layout.tsx` | `layout.tsx` | Next.js App Router 布局文件 |
| **业务组件** | PascalCase | `BoxManagement.tsx` | 业务功能组件 |
| **通用组件** | PascalCase | `ErrorBoundary.tsx` | 可复用的通用组件 |
| **Hook 文件** | camelCase | `useErrorHandler.ts` | 自定义 React Hook |
| **工具函数** | camelCase | `apiResponse.ts` | 工具和帮助函数 |
| **类型文件** | camelCase | `box.ts` | TypeScript 类型定义 |
| **校验文件** | camelCase | `box.ts` | Zod 校验规则 |
| **API 路由** | camelCase | `getList/route.ts` | API 接口路由文件 |
| **常量文件** | camelCase | `api.ts` | 常量定义文件 |

## 组件开发规范

### 1. 组件结构

#### 标准组件模板

```typescript
// src/components/shared/ExampleComponent.tsx
'use client';

import React, { useState } from 'react';
import { Button, Card, message } from 'antd';
import { ExampleComponentProps } from '@/types/components';
import { useErrorHandler } from '@/lib/hooks/useErrorHandler';

/**
 * 示例组件
 * @param props 组件属性
 * @returns JSX 元素
 */
export const ExampleComponent: React.FC<ExampleComponentProps> = ({
  title,
  children,
  onAction,
  className = '',
  ...rest
}) => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  
  // 错误处理Hook
  const { handleResult } = useErrorHandler();

  // 事件处理函数
  const handleAction = async () => {
    setLoading(true);
    try {
      const result = await onAction?.();
      handleResult(result, '操作成功');
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card 
      title={title}
      className={`example-component ${className}`}
      {...rest}
    >
      {children}
      <Button 
        type="primary" 
        loading={loading}
        onClick={handleAction}
      >
        执行操作
      </Button>
    </Card>
  );
};

export default ExampleComponent;
```

#### 组件类型定义

```typescript
// src/types/components.ts
export interface ExampleComponentProps {
  title: string;
  children?: React.ReactNode;
  onAction?: () => Promise<any>;
  className?: string;
  [key: string]: any; // 其他 props
}
```

### 2. Tabs组件标准实现

#### 2.1 统一Tabs实现规范

项目中所有带有多Tab页的组件必须使用统一的实现方式，避免混合使用不同的Tabs API。

**✅ 标准实现方式**（推荐）:
```typescript
// 使用 items 属性方式 - 项目标准
const tabItems = [
  {
    key: 'tab1',
    label: 'Tab标题1',
    children: (
      <Card>
        {/* Tab内容，必须用Card包装 */}
        <div>Tab1的具体内容</div>
      </Card>
    )
  },
  {
    key: 'tab2', 
    label: 'Tab标题2',
    children: (
      <Card>
        {/* Tab内容，必须用Card包装 */}
        <div>Tab2的具体内容</div>
      </Card>
    )
  }
];

return (
  <div>
    <Title level={2}>页面标题</Title>
    
    <Tabs
      activeKey={activeTab}
      onChange={setActiveTab}
      items={tabItems}
    />
  </div>
);
```

**❌ 避免的实现方式**:
```typescript
// 不要使用 Tabs.TabPane 组件方式
<Tabs activeKey={activeTab} onChange={setActiveTab}>
  <Tabs.TabPane tab="Tab标题1" key="tab1">
    <div>内容1</div>
  </Tabs.TabPane>
  <Tabs.TabPane tab="Tab标题2" key="tab2">
    <div>内容2</div>
  </Tabs.TabPane>
</Tabs>

// 不要混合使用 items 和 TabPane
<Tabs 
  activeKey={activeTab} 
  onChange={setActiveTab}
  items={[{ key: 'tab1', label: '标题1', children: <div>内容</div> }]}
>
  <Tabs.TabPane tab="标题2" key="tab2">
    <div>内容2</div>
  </Tabs.TabPane>
</Tabs>
```

#### 2.2 完整Tabs页面实现示例

```typescript
// src/app/admin/example/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Tabs, Card, Table, Button, Row, Col, Input, Select, Space } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title } = Typography;
const { Option } = Select;

export default function ExampleTabsPage() {
  // Tab状态管理
  const [activeTab, setActiveTab] = useState('tab1');
  
  // 错误处理Hook
  const { execute, loading } = useAsyncError();

  // Tab1数据状态
  const [tab1Data, setTab1Data] = useState([]);
  const [tab1Keyword, setTab1Keyword] = useState('');
  
  // Tab2数据状态  
  const [tab2Data, setTab2Data] = useState([]);

  // 数据获取函数
  const fetchTab1Data = async () => {
    const result = await execute(async () => {
      return await someApi.getTab1List({ keyword: tab1Keyword });
    }, '获取Tab1数据');
    
    if (result) {
      setTab1Data(result.list || []);
    }
  };

  const fetchTab2Data = async () => {
    const result = await execute(async () => {
      return await someApi.getTab2List();
    }, '获取Tab2数据');
    
    if (result) {
      setTab2Data(result.list || []);
    }
  };

  // Tab切换时加载对应数据
  useEffect(() => {
    if (activeTab === 'tab1') {
      fetchTab1Data();
    } else if (activeTab === 'tab2') {
      fetchTab2Data();
    }
  }, [activeTab]);

  // 搜索处理
  const handleTab1Search = () => {
    fetchTab1Data();
  };

  const handleTab1Reset = () => {
    setTab1Keyword('');
    fetchTab1Data();
  };

  // Tab内容配置
  const tabItems = [
    {
      key: 'tab1',
      label: '数据管理',
      children: (
        <Card>
          {/* 搜索筛选区域 */}
          <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Input
                placeholder="搜索关键词"
                prefix={<SearchOutlined />}
                allowClear
                value={tab1Keyword}
                onChange={(e) => setTab1Keyword(e.target.value)}
                onPressEnter={handleTab1Search}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleTab1Search}
              >
                搜索
              </Button>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleTab1Reset}
              >
                重置
              </Button>
            </Col>
            <Col flex="auto" style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => console.log('新增')}
              >
                新增数据
              </Button>
            </Col>
          </Row>

          {/* 数据表格 */}
          <Table
            dataSource={tab1Data}
            loading={loading}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            bordered
            size="middle"
            locale={{ emptyText: '暂无数据' }}
            // ... 其他Table配置
          />
        </Card>
      )
    },
    {
      key: 'tab2',
      label: '配置管理', 
      children: (
        <Card>
          {/* Tab2的具体内容 */}
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col flex="auto" style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => console.log('新增配置')}
                >
                  新增配置
                </Button>
              </Col>
            </Row>
          </div>

          <Table
            dataSource={tab2Data}
            loading={loading}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            bordered
            size="middle"
            locale={{ emptyText: '暂无数据' }}
            // ... 其他Table配置
          />
        </Card>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>示例Tabs页面</Title>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      {/* 其他模态框等组件 */}
    </div>
  );
}
```

#### 2.3 Tabs组件开发规范

1. **统一使用items属性**: 所有Tabs组件必须使用`items`属性而不是`Tabs.TabPane`
2. **Card包装内容**: 每个Tab的内容必须用`<Card>`组件包装
3. **状态管理**: 使用`activeTab`状态管理当前活跃的Tab
4. **数据懒加载**: 在Tab切换时才加载对应的数据，避免初始化时加载所有数据
5. **统一布局**: 搜索筛选区域使用`Row`和`Col`布局，右对齐操作按钮
6. **统一样式**: Table组件使用`bordered`、`size="middle"`、`locale={{ emptyText: '暂无数据' }}`

#### 2.4 常见问题和解决方案

**问题1: 混合使用items和TabPane**
```typescript
// ❌ 错误：混合使用
<Tabs items={[...]} onChange={setActiveTab}>
  <Tabs.TabPane tab="额外Tab" key="extra">内容</Tabs.TabPane>
</Tabs>

// ✅ 正确：统一使用items
const allTabItems = [
  ...existingItems,
  { key: 'extra', label: '额外Tab', children: <Card>内容</Card> }
];
<Tabs items={allTabItems} onChange={setActiveTab} />
```

**问题2: Tab内容没有Card包装**
```typescript
// ❌ 错误：直接放置内容
{
  key: 'tab1',
  label: 'Tab1',
  children: <div>直接内容</div>
}

// ✅ 正确：用Card包装
{
  key: 'tab1', 
  label: 'Tab1',
  children: (
    <Card>
      <div>Card包装的内容</div>
    </Card>
  )
}
```

**问题3: 缺少统一的搜索布局**
```typescript
// ❌ 错误：布局不统一
<div style={{ marginBottom: 16 }}>
  <Input placeholder="搜索" />
  <Button>搜索</Button>
  <Button style={{ float: 'right' }}>新增</Button>
</div>

// ✅ 正确：使用Row/Col统一布局
<Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
  <Col span={6}>
    <Input placeholder="搜索" />
  </Col>
  <Col>
    <Button>搜索</Button>
  </Col>
  <Col flex="auto" style={{ textAlign: 'right' }}>
    <Button type="primary">新增</Button>
  </Col>
</Row>
```

### 3. 页面组件规范

#### 页面组件模板

```typescript
// src/app/admin/example/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Input, Select, DatePicker, message } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { ExampleItem, ExampleListParams } from '@/types/example';
import { exampleApi } from '@/services/adminApi';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 示例管理页面
 */
export default function ExamplePage() {
  // 状态管理
  const [searchText, setSearchText] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [dataList, setDataList] = useState<ExampleItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 错误处理Hook
  const { errorState, clearError, handleResult } = useErrorHandler();
  const { execute, loading } = useAsyncError();

  // 获取数据列表
  const fetchDataList = async (params: ExampleListParams) => {
    const result = await execute(async () => {
      const { current, pageSize } = pagination;
      
      const requestParams: ExampleListParams = {
        page: current,
        pageSize,
        ...params,
      };
      
      return await exampleApi.getList(requestParams);
    }, '获取数据列表');

    if (result) {
      setDataList(result.list || []);
      setPagination(prev => ({
        ...prev,
        total: result.pagination?.total || 0
      }));
    } else {
      setDataList([]);
      setPagination(prev => ({ ...prev, total: 0 }));
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchDataList({ page: 1 });
  }, []);

  // 处理搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    
    const searchParams: any = { page: 1 };
    
    if (searchText) {
      searchParams.keyword = searchText;
    }
    
    if (statusFilter) {
      searchParams.status = parseInt(statusFilter);
    }
    
    if (dateRange?.[0] && dateRange?.[1]) {
      searchParams.startTime = dateRange[0].format('YYYY-MM-DD HH:mm:ss');
      searchParams.endTime = dateRange[1].format('YYYY-MM-DD HH:mm:ss');
    }
    
    clearError();
    fetchDataList(searchParams);
  };

  // 重置筛选条件
  const handleReset = () => {
    setSearchText('');
    setStatusFilter('');
    setDateRange(null);
    setPagination(prev => ({ ...prev, current: 1 }));
    clearError();
    fetchDataList({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
    fetchDataList({
      page: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    });
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    const result = await execute(async () => {
      return await exampleApi.delete(id);
    }, '删除数据');

    if (result) {
      fetchDataList({ page: 1 });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center' as const,
      render: (status: number) => (
        <span className={status === 1 ? 'text-green-600' : 'text-gray-400'}>
          {status === 1 ? '启用' : '禁用'}
        </span>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      align: 'center' as const,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: ExampleItem) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditXXX(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            onConfirm={() => handleXXXX(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理编辑
  const handleEdit = (item: ExampleItem) => {
    // 编辑逻辑
    window.location.href = `/admin/example/${item.id}`;
  };

  return (
    <div className="p-6">
      <Card
        title="示例管理"
        extra={
          <Button type="primary" icon={<PlusOutlined />}>
            新增
          </Button>
        }
      >
        {/* 搜索筛选区域 */}
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="搜索关键词"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
            <Select
              placeholder="选择状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
            >
              <Option value="1">启用</Option>
              <Option value="0">禁用</Option>
            </Select>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </div>
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={dataList}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          rowKey="id"
          onChange={handleTableChange}
        />
      </Card>
    </div>
  );
}
```

### 4. 自定义 Hooks

#### Hook 开发规范

```typescript
// src/lib/hooks/useExampleData.ts
import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { exampleApi } from '@/services/adminApi';
import { ExampleItem, ExampleListParams } from '@/types/example';
import { PaginatedData } from '@/types/common';

export interface UseExampleDataReturn {
  data: ExampleItem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  fetchData: (params?: ExampleListParams) => Promise<void>;
  createItem: (data: Partial<ExampleItem>) => Promise<void>;
  updateItem: (id: number, data: Partial<ExampleItem>) => Promise<void>;
  deleteItem: (id: number) => Promise<void>;
}

/**
 * 示例数据管理 Hook
 */
export const useExampleData = (): UseExampleDataReturn => {
  const [data, setData] = useState<ExampleItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取数据
  const fetchData = useCallback(async (params?: ExampleListParams) => {
    setLoading(true);
    try {
      const response = await exampleApi.getList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });

      if (response.success) {
        const { list, pagination: paginationData } = response.data as PaginatedData<ExampleItem>;
        setData(list);
        setPagination(prev => ({
          ...prev,
          total: paginationData.total,
        }));
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [pagination.current, pagination.pageSize]);

  // 创建项目
  const createItem = useCallback(async (itemData: Partial<ExampleItem>) => {
    try {
      const response = await exampleApi.create(itemData);
      if (response.success) {
        message.success('创建成功');
        fetchData();
      }
    } catch (error) {
      message.error('创建失败');
    }
  }, [fetchData]);

  // 更新项目
  const updateItem = useCallback(async (id: number, itemData: Partial<ExampleItem>) => {
    try {
      const response = await exampleApi.update(id, itemData);
      if (response.success) {
        message.success('更新成功');
        fetchData();
      }
    } catch (error) {
      message.error('更新失败');
    }
  }, [fetchData]);

  // 删除项目
  const deleteItem = useCallback(async (id: number) => {
    try {
      const response = await exampleApi.delete(id);
      if (response.success) {
        message.success('删除成功');
        fetchData();
      }
    } catch (error) {
      message.error('删除失败');
    }
  }, [fetchData]);

  // 分页处理
  const handlePaginationChange = useCallback((page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize,
    }));
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    pagination: {
      ...pagination,
      onChange: handlePaginationChange,
    },
    fetchData,
    createItem,
    updateItem,
    deleteItem,
  };
};
```

### 5. TypeScript 类型规范

#### 严格类型定义

```typescript
// 避免使用 any，使用具体类型
// ❌ 错误示例
const handleData = (data: any) => {
  // ...
};

// ✅ 正确示例
interface UserData {
  id: number;
  name: string;
  email: string;
  status: 0 | 1;
}

const handleData = (data: UserData) => {
  // ...
};

// 使用联合类型和字面量类型
type Status = 'pending' | 'approved' | 'rejected';

// 使用泛型提高复用性
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

interface PaginatedData<T> {
  list: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}
```

## 状态管理

### 1. 本地状态管理

#### useState 使用规范

```typescript
// 简单状态
const [loading, setLoading] = useState(false);
const [visible, setVisible] = useState(false);

// 复杂状态使用 useReducer
interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

const formReducer = (state: FormState, action: any) => {
  switch (action.type) {
    case 'SET_VALUE':
      return {
        ...state,
        values: { ...state.values, [action.field]: action.value },
      };
    case 'SET_ERROR':
      return {
        ...state,
        errors: { ...state.errors, [action.field]: action.error },
      };
    default:
      return state;
  }
};

const [formState, dispatch] = useReducer(formReducer, {
  values: {},
  errors: {},
  touched: {},
});
```

### 2. 全局状态管理

#### Context 使用示例

```typescript
// src/lib/contexts/AppContext.tsx
import React, { createContext, useContext, useReducer } from 'react';

interface AppState {
  user: User | null;
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
}

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<any>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

const appReducer = (state: AppState, action: any): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    default:
      return state;
  }
};

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, {
    user: null,
    theme: 'light',
    sidebarCollapsed: false,
  });

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};
```

## API服务层架构

### 1. 服务层设计模式

项目采用统一的API服务层架构，所有API调用都通过`src/services/adminApi.ts`进行封装。

#### adminApi.ts结构分析

```typescript
// src/services/adminApi.ts 核心结构
import { resultApi } from '@/lib/utils/request';
import { Result, PaginatedData } from '@/types/common';

// 盒型相关API - 使用Result类型
export const boxApi = {
  /**
   * 获取盒型列表 - Result类型版本
   */
  getList: (params: BoxListParams): Promise<Result<PaginatedData<Box>>> => {
    return resultApi.post<PaginatedData<Box>>('/api/v1/admin/box/getList', {
      params,
      showError: true,
    });
  },

  /**
   * 获取盒型详情 - Result类型版本
   */
  getDetail: (id: number): Promise<Result<Box>> => {
    return resultApi.post<Box>(`/api/v1/admin/box/getDetail`, {
      params: { id },
      showError: true,
    });
  },

  /**
   * 创建盒型 - Result类型版本
   */
  create: (data: BoxCreateParams): Promise<Result<Box>> => {
    return resultApi.post<Box>('/api/v1/admin/box/create', data, {
      showError: true,
      showSuccess: true,
    });
  },

  /**
   * 更新盒型 - Result类型版本
   */
  update: (data: BoxUpdateParams & { id: number }): Promise<Result<Box>> => {
    return resultApi.post<Box>('/api/v1/admin/box/update', data, {
      showError: true,
      showSuccess: true,
    });
  },

  /**
   * 删除盒型 - Result类型版本
   */
  delete: (id: number): Promise<Result<{ id: number }>> => {
    return resultApi.post<{ id: number }>('/api/v1/admin/box/delete', {
      params: { id },
      showError: true,
      showSuccess: true,
    });
  },

  /**
   * 获取盒型图片URL
   */
  getImageUrl: (id: number) => {
    return `/api/v1/admin/box/getImage?id=${id}`;
  },
};
```

### 2. Result类型vs传统Promise对比

#### Result类型的优势

1. **统一的错误处理**: 所有API调用都返回统一的Result类型
2. **类型安全**: TypeScript严格类型检查
3. **错误信息标准化**: 统一的错误码和错误消息格式
4. **更好的调试体验**: 清晰的成功/失败状态

```typescript
// ❌ 传统Promise方式
try {
  const response = await fetch('/api/box/list');
  const data = await response.json();
  if (response.ok) {
    setBoxList(data.list);
  } else {
    message.error(data.message || '请求失败');
  }
} catch (error) {
  message.error('网络错误');
}

// ✅ Result类型方式
const { handleResult } = useErrorHandler();
const result = await boxApi.getList(params);
const data = handleResult(result, '获取盒型列表');

if (data) {
  setBoxList(data.list);
}
// 错误已经由handleResult自动处理
```

### 3. API调用配置选项

#### resultApi配置参数

```typescript
interface RequestOptions {
  params?: any;           // 请求参数
  showError?: boolean;    // 是否自动显示错误消息
  showSuccess?: boolean;  // 是否自动显示成功消息
  timeout?: number;       // 请求超时时间
  headers?: Record<string, string>; // 自定义请求头
}

// 使用示例
const result = await boxApi.getList({
  page: 1,
  pageSize: 10,
  keyword: '搜索关键词'
}, {
  showError: true,    // 自动显示错误消息
  timeout: 5000,      // 5秒超时
});
```

### 4. 错误处理集成

#### 与前端错误处理的配合

```typescript
// 完整的API调用示例
const BoxManagementComponent: React.FC = () => {
  const [boxList, setBoxList] = useState<Box[]>([]);
  const { errorState, clearError, handleResult } = useErrorHandler();
  const { execute, loading } = useAsyncError();

  // 方式一：使用handleResult处理Result类型响应
  const fetchBoxListWithHandleResult = async (params: BoxListParams) => {
    const result = await boxApi.getList(params);
    const data = handleResult(result, '获取盒型列表');
    
    if (data) {
      setBoxList(data.list || []);
    }
  };

  // 方式二：使用execute包装异步操作
  const fetchBoxListWithExecute = async (params: BoxListParams) => {
    const result = await execute(async () => {
      return await boxApi.getList(params);
    }, '获取盒型列表');

    if (result) {
      setBoxList(result.list || []);
    }
  };

  // 创建操作示例
  const handleCreate = async (formData: BoxCreateParams) => {
    const result = await execute(async () => {
      return await boxApi.create(formData);
    }, '创建盒型');

    if (result) {
      message.success('创建成功');
      fetchBoxListWithExecute({ page: 1 }); // 重新加载列表
    }
  };

  return (
    <div>
      {/* 错误显示 */}
      {errorState.hasError && (
        <Alert
          message={errorState.error?.message}
          type="error"
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}
      
      {/* 数据列表 */}
      <Table
        dataSource={boxList}
        loading={loading}
        // ... 其他配置
      />
    </div>
  );
};
```

### 5. 服务层最佳实践

#### API命名规范

```typescript
// 统一的API命名规范
export const moduleApi = {
  // 查询操作
  getList: (params: ListParams) => Promise<Result<PaginatedData<T>>>,
  getDetail: (id: number) => Promise<Result<T>>,
  
  // 修改操作
  create: (data: CreateParams) => Promise<Result<T>>,
  update: (data: UpdateParams) => Promise<Result<T>>,
  delete: (id: number) => Promise<Result<{ id: number }>>,
  
  // 特殊操作
  getImageUrl: (id: number) => string,
  export: (params: ExportParams) => Promise<Result<Blob>>,
  import: (file: File) => Promise<Result<ImportResult>>,
};
```

#### 类型定义规范

```typescript
// 请求参数类型
export interface BoxListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
}

// 创建参数类型
export interface BoxCreateParams {
  name: string;
  status: number;
  description?: string | null;
  processingFee: number;
  processingBasePrice: number;
  attributes?: BoxAttribute[];
  parts?: BoxPart[];
  images?: BoxImage[];
  packaging?: BoxPackaging;
}

// 更新参数类型
export interface BoxUpdateParams extends BoxCreateParams {
  id: number;
  deleteAttributes?: number[];
  deleteParts?: number[];
  deleteFormulas?: number[];
  deleteImages?: number[];
}
```

#### 错误处理策略

```typescript
// 不同场景的错误处理策略

// 1. 列表查询 - 显示错误但不阻断用户操作
const fetchList = async () => {
  const result = await boxApi.getList(params);
  const data = handleResult(result, '获取列表');
  
  if (data) {
    setDataList(data.list);
  } else {
    setDataList([]); // 失败时显示空列表
  }
};

// 2. 创建/更新操作 - 显示错误并阻断后续操作
const handleSave = async () => {
  const result = await execute(async () => {
    return await boxApi.create(formData);
  }, '保存数据');

  if (result) {
    // 只有成功时才执行后续操作
    message.success('保存成功');
    router.push('/admin/box');
  }
  // 失败时不执行任何操作，错误已由execute处理
};

// 3. 删除操作 - 需要用户确认
const handleDelete = async (id: number) => {
  Modal.confirm({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除吗？',
    onOk: async () => {
      const result = await execute(async () => {
        return await boxApi.delete(id);
      }, '删除数据');

      if (result) {
        fetchList(); // 重新加载列表
      }
    },
  });
};
```

## 样式开发指南

### 1. Tailwind CSS 使用规范

#### 常用样式类

```typescript
// 布局类
const layoutClasses = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  grid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
};

// 间距类
const spacingClasses = {
  section: 'py-8 px-6',
  card: 'p-6',
  button: 'px-4 py-2',
};

// 颜色类
const colorClasses = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700',
  success: 'bg-green-600 text-white hover:bg-green-700',
  danger: 'bg-red-600 text-white hover:bg-red-700',
  warning: 'bg-yellow-600 text-white hover:bg-yellow-700',
};
```

#### 响应式设计

```typescript
// 响应式组件示例
const ResponsiveCard: React.FC = ({ children }) => {
  return (
    <div className="
      w-full 
      sm:w-1/2 
      md:w-1/3 
      lg:w-1/4 
      p-4
      bg-white 
      rounded-lg 
      shadow-md 
      hover:shadow-lg 
      transition-shadow
    ">
      {children}
    </div>
  );
};
```

### 2. Ant Design 主题定制

#### 主题配置

```typescript
// src/app/providers.tsx
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

const theme = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    borderRadius: 6,
    fontSize: 14,
  },
  components: {
    Button: {
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
    },
  },
};

export const AntdProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ConfigProvider theme={theme} locale={zhCN}>
      {children}
    </ConfigProvider>
  );
};
```

### 3. 自定义样式

#### CSS Modules 使用

```css
/* src/app/admin/box/styles.module.css */
.boxCard {
  @apply bg-white rounded-lg shadow-md p-6;
  transition: all 0.3s ease;
}

.boxCard:hover {
  @apply shadow-lg transform -translate-y-1;
}

.statusBadge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.statusBadge.active {
  @apply bg-green-100 text-green-800;
}

.statusBadge.inactive {
  @apply bg-gray-100 text-gray-800;
}
```

```typescript
// 在组件中使用
import styles from './styles.module.css';

const BoxCard: React.FC<BoxCardProps> = ({ box }) => {
  return (
    <div className={styles.boxCard}>
      <span className={`${styles.statusBadge} ${box.status === 1 ? styles.active : styles.inactive}`}>
        {box.status === 1 ? '启用' : '禁用'}
      </span>
    </div>
  );
};
```

## 表单开发

### 1. React Hook Form 集成

#### 基础表单示例

```typescript
// src/lib/components/forms/ExampleForm.tsx
import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, Input, Button, Select, message } from 'antd';
import { exampleSchema } from '@/lib/validations/example';
import { ExampleFormData } from '@/types/example';

interface ExampleFormProps {
  initialData?: Partial<ExampleFormData>;
  onSubmit: (data: ExampleFormData) => Promise<void>;
  loading?: boolean;
}

export const ExampleForm: React.FC<ExampleFormProps> = ({
  initialData,
  onSubmit,
  loading = false,
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ExampleFormData>({
    resolver: zodResolver(exampleSchema),
    defaultValues: initialData,
  });

  const handleFormSubmit = async (data: ExampleFormData) => {
    try {
      await onSubmit(data);
      message.success('提交成功');
      reset();
    } catch (error) {
      message.error('提交失败');
    }
  };

  return (
    <Form layout="vertical" onFinish={handleSubmit(handleFormSubmit)}>
      <Form.Item
        label="名称"
        validateStatus={errors.name ? 'error' : ''}
        help={errors.name?.message}
      >
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <Input {...field} placeholder="请输入名称" />
          )}
        />
      </Form.Item>

      <Form.Item
        label="类型"
        validateStatus={errors.type ? 'error' : ''}
        help={errors.type?.message}
      >
        <Controller
          name="type"
          control={control}
          render={({ field }) => (
            <Select {...field} placeholder="请选择类型">
              <Select.Option value="type1">类型1</Select.Option>
              <Select.Option value="type2">类型2</Select.Option>
            </Select>
          )}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};
```

### 2. 表单验证

#### Zod 验证模式

```typescript
// src/lib/validations/example.ts
import { z } from 'zod';

export const exampleSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  type: z.enum(['type1', 'type2'], { required_error: '请选择类型' }),
  description: z.string().optional(),
  price: z.number().min(0, '价格不能小于0'),
  status: z.number().int().min(0).max(1),
});

export type ExampleFormData = z.infer<typeof exampleSchema>;
```

## 错误处理

### 1. 错误处理Hook详解

#### useErrorHandler Hook

项目采用统一的错误处理机制，通过`useErrorHandler` Hook提供标准化的错误处理功能。

```typescript
// src/lib/hooks/useErrorHandler.ts 使用示例
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';

const MyComponent: React.FC = () => {
  const { errorState, clearError, handleResult } = useErrorHandler();
  const { execute, loading } = useAsyncError();

  // 使用handleResult处理Result类型的API响应
  const handleApiCall = async () => {
    const result = await boxApi.getList(params);
    const data = handleResult(result, '获取盒型列表');
    
    if (data) {
      // 处理成功结果
      setBoxList(data.list);
    }
    // 错误已经由handleResult自动处理
  };

  // 使用execute处理异步操作
  const handleAsyncOperation = async () => {
    const result = await execute(async () => {
      return await boxApi.create(formData);
    }, '创建盒型');

    if (result) {
      // 处理成功结果
      message.success('创建成功');
    }
    // 错误已经由execute函数处理
  };

  return (
    <div>
      {errorState.hasError && (
        <div className="error-message">
          {errorState.message}
          <Button onClick={clearError}>清除错误</Button>
        </div>
      )}
      <Button loading={loading} onClick={handleAsyncOperation}>
        执行操作
      </Button>
    </div>
  );
};
```

#### useErrorHandler API说明

```typescript
interface ErrorHandler {
  /** 错误状态 */
  errorState: ErrorState;
  /** 处理Result类型的响应 */
  handleResult: <T>(result: Result<T>, action?: string) => T | null;
  /** 手动设置错误 */
  setError: (error: ErrorInfo) => void;
  /** 清除错误 */
  clearError: () => void;
  /** 清除所有错误历史 */
  clearErrorHistory: () => void;
  /** 重试上次操作 */
  retry: () => void;
  /** 设置重试回调 */
  setRetryCallback: (callback: () => void | Promise<void>) => void;
  /** 获取格式化的错误消息 */
  getFormattedError: (error?: ErrorInfo) => string;
  /** 检查是否为特定类型的错误 */
  isErrorType: (errorCode: ErrorCode | number) => boolean;
}
```

#### useAsyncError Hook

用于处理异步操作的错误处理Hook：

```typescript
const { execute, loading } = useAsyncError();

// 执行异步操作
const result = await execute(async () => {
  return await someAsyncOperation();
}, '操作描述');
```

### 2. Result类型API规范

#### Result类型定义

项目使用统一的Result类型来包装API响应：

```typescript
export interface Result<T> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    details?: any;
  };
}
```

#### Result类型API使用模式

```typescript
// 标准的API调用模式
const fetchData = async () => {
  const { handleResult } = useErrorHandler();
  
  // 调用API
  const result = await boxApi.getList(params);
  
  // 使用handleResult处理响应
  const data = handleResult(result, '获取数据列表');
  
  if (data) {
    // 成功时处理数据
    setDataList(data.list);
    setPagination(data.pagination);
  }
  // 错误已经由handleResult自动处理
};
```

#### 错误处理最佳实践

1. **统一使用useErrorHandler**: 所有组件都应该使用useErrorHandler来处理错误
2. **明确操作描述**: 在handleResult和execute中提供清晰的操作描述
3. **避免重复错误处理**: 不要在handleResult之后再次处理错误
4. **合理使用重试机制**: 对于网络错误等临时性错误，可以使用重试功能

```typescript
// 完整的错误处理示例
const BoxListComponent: React.FC = () => {
  const [boxList, setBoxList] = useState<Box[]>([]);
  const { errorState, clearError, handleResult } = useErrorHandler();
  const { execute, loading } = useAsyncError();

  // 获取数据
  const fetchBoxList = async (params: BoxListParams) => {
    const result = await execute(async () => {
      return await boxApi.getList(params);
    }, '获取盒型列表');

    if (result) {
      setBoxList(result.list || []);
    }
  };

  // 删除操作
  const handleDelete = async (id: number) => {
    const result = await execute(async () => {
      return await boxApi.delete(id);
    }, '删除盒型');

    if (result) {
      fetchBoxList({ page: 1 }); // 重新加载列表
    }
  };

  return (
    <div>
      {/* 错误显示 */}
      {errorState.hasError && (
        <Alert
          message={errorState.error?.message}
          type="error"
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}
      
      {/* 数据列表 */}
      <Table
        dataSource={boxList}
        loading={loading}
        // ... 其他配置
      />
    </div>
  );
};
```

### 3. 错误边界组件

```typescript
// src/lib/components/ErrorBoundary.tsx
import React from 'react';
import { Result, Button } from 'antd';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('错误边界捕获到错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Result
          status="500"
          title="页面出现错误"
          subTitle="抱歉，页面出现了错误，请刷新页面重试。"
          extra={
            <Button type="primary" onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          }
        />
      );
    }

    return this.props.children;
  }
}
```

### 4. 错误码系统

项目使用统一的错误码系统，定义在`src/lib/constants/errorCodes.ts`中：

```typescript
// 错误码分类
export enum ErrorCode {
  // 通用错误 (1000-1999)
  INVALID_PARAMETERS = 1000, // 参数验证失败
  MISSING_REQUIRED_FIELD = 1001, // 缺少必填字段
  DUPLICATE_ENTRY = 1007, // 数据已存在
  RESOURCE_NOT_FOUND = 1008, // 资源不存在
  
  // 认证授权错误 (2000-2999)
  LOGIN_REQUIRED = 2000, // 请先登录
  INVALID_TOKEN = 2001, // 无效的访问令牌
  TOKEN_EXPIRED = 2002, // 访问令牌已过期
  
  // 盒型业务错误 (3000-3099)
}
```

## 性能监控

### 1. 性能监控 Hook

```typescript
// 性能监控 Hook
const usePerformanceMonitor = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTime > 100) { // 超过100ms的渲染警告
        console.warn(`⚠️ ${componentName} 渲染耗时: ${renderTime.toFixed(2)}ms`);
      }
    };
  });
};

// 在组件中使用
const MyComponent: React.FC = () => {
  usePerformanceMonitor('MyComponent');
  
  return <div>...</div>;
};
```

## 代码分割和懒加载

### 1. 路由级别的代码分割

```typescript
import dynamic from 'next/dynamic';

const BoxManagement = dynamic(() => import('./BoxManagement'), {
  loading: () => <div className="flex justify-center p-8">加载中...</div>,
  ssr: false, // 禁用服务端渲染
});
```

### 2. 组件级别的懒加载

```typescript
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Spin size="large" />,
});
```

### 3. 条件加载

```typescript
import dynamic from 'next/dynamic';

const ConditionalComponent = dynamic(
  () => import('./ConditionalComponent'),
  { ssr: false }
);
```

## 内存泄漏检测

### 1. 内存泄漏检测工具

```typescript
// 内存泄漏检测工具
const useMemoryLeakDetector = (componentName: string) => {
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    const intervals: NodeJS.Timeout[] = [];
    
    // 记录定时器
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    
    window.setTimeout = (...args) => {
      const timer = originalSetTimeout(...args);
      timers.push(timer);
      return timer;
    };
    
    window.setInterval = (...args) => {
      const interval = originalSetInterval(...args);
      intervals.push(interval);
      return interval;
    };
    
    return () => {
      // 清理定时器
      timers.forEach(clearTimeout);
      intervals.forEach(clearInterval);
      
      // 恢复原始方法
      window.setTimeout = originalSetTimeout;
      window.setInterval = originalSetInterval;
      
      console.log(`🧹 ${componentName} 清理了 ${timers.length} 个定时器和 ${intervals.length} 个间隔器`);
    };
  }, [componentName]);
};
```

## 测试和质量保证

### 1. 单元测试示例

```typescript
// src/lib/utils/__tests__/formula.test.ts
import { calculateFormula, validateFormula, extractFormulaVariables } from '../formula';

describe('公式计算工具', () => {
  describe('calculateFormula', () => {
    it('应该正确计算简单公式', () => {
      const result = calculateFormula('2 + 3', {});
      expect(result).toBe(5);
    });

    it('应该正确计算包含变量的公式', () => {
      const result = calculateFormula('length * width', { length: 10, width: 5 });
      expect(result).toBe(50);
    });

    it('应该处理复杂公式', () => {
      const result = calculateFormula('(length + width) * 2', { length: 10, width: 5 });
      expect(result).toBe(30);
    });
  });

  describe('validateFormula', () => {
    it('应该验证有效公式', () => {
      const result = validateFormula('length * width');
      expect(result.isValid).toBe(true);
    });

    it('应该检测无效公式', () => {
      const result = validateFormula('length * ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('extractFormulaVariables', () => {
    it('应该提取公式中的变量', () => {
      const variables = extractFormulaVariables('length * width + height');
      expect(variables).toEqual(['length', 'width', 'height']);
    });
  });
});
```

### 2. 组件测试示例

```typescript
// src/lib/components/__tests__/FormulaEditor.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FormulaEditor } from '../FormulaEditor';

describe('FormulaEditor', () => {
  const mockAttributes = [
    { name: '长度', code: 'length', value: 100 },
    { name: '宽度', code: 'width', value: 50 },
  ];

  it('应该正确渲染组件', () => {
    render(<FormulaEditor attributes={mockAttributes} />);
    
    expect(screen.getByPlaceholderText(/请输入公式/)).toBeInTheDocument();
    expect(screen.getByText('长度 (length)')).toBeInTheDocument();
    expect(screen.getByText('宽度 (width)')).toBeInTheDocument();
  });

  it('应该处理公式输入', async () => {
    const mockOnChange = jest.fn();
    render(<FormulaEditor onChange={mockOnChange} attributes={mockAttributes} />);
    
    const input = screen.getByPlaceholderText(/请输入公式/);
    fireEvent.change(input, { target: { value: 'length * width' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('length * width');
    
    await waitFor(() => {
      expect(screen.getByText('length')).toBeInTheDocument();
      expect(screen.getByText('width')).toBeInTheDocument();
    });
  });

  it('应该显示公式验证错误', async () => {
    render(<FormulaEditor attributes={mockAttributes} />);
    
    const input = screen.getByPlaceholderText(/请输入公式/);
    fireEvent.change(input, { target: { value: 'length * ' } });
    
    await waitFor(() => {
      expect(screen.getByText(/语法错误/)).toBeInTheDocument();
    });
  });
});
```

## 开发环境配置

### 1. 环境变量配置

```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_ENV=development
DATABASE_URL="mysql://user:password@localhost:3306/ycbz"
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
```

### 2. 开发服务器配置

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
```

## 常见问题和解决方案

### 1. Hydration 错误

```typescript
// 解决 Hydration 不匹配问题
const ClientOnlyComponent: React.FC = ({ children }) => {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return null;
  }

  return <>{children}</>;
};

// 使用示例
const MyPage: React.FC = () => {
  return (
    <div>
      <h1>服务端渲染内容</h1>
      <ClientOnlyComponent>
        <div>仅客户端渲染的内容</div>
      </ClientOnlyComponent>
    </div>
  );
};
```

### 2. 状态更新问题

```typescript
// 避免直接修改状态
// ❌ 错误做法
const updateItem = (id: number, newData: any) => {
  const item = items.find(item => item.id === id);
  item.name = newData.name; // 直接修改
  setItems(items); // 不会触发重新渲染
};

// ✅ 正确做法
const updateItem = (id: number, newData: any) => {
  setItems(prevItems => 
    prevItems.map(item => 
      item.id === id ? { ...item, ...newData } : item
    )
  );
};
```

### 3. 异步操作处理

```typescript
// 处理组件卸载后的异步操作
const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const executeAsync = async (operation: () => Promise<any>) => {
    setLoading(true);
    try {
      const result = await operation();
      if (isMountedRef.current) {
        // 只在组件仍然挂载时更新状态
        return result;
      }
    } catch (error) {
      if (isMountedRef.current) {
        console.error('异步操作错误:', error);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  return { loading, executeAsync };
};
```

---

*本文档最后更新时间：2024年12月*

## 加工费模块实现指南

### 1. 模块概述

加工费模块是工艺工资管理的重要组成部分，采用单文件实现模式，包含两个主要功能：
- **加工费配置**：管理各种加工费项目的单价、单位、起步价等信息
- **固定参数配置**：管理PVC贴膜、开槽工资、吸塑版等固定费用参数

### 2. 技术架构

#### 2.1 文件结构
```
src/app/admin/craftSalary/processingFee/
└── page.tsx                           # 单文件实现，包含所有功能

src/app/api/v1/admin/craftSalary/
├── processingFee/                     # 加工费API
│   ├── getList/route.ts              # 获取列表
│   ├── create/route.ts               # 创建
│   ├── update/route.ts               # 更新
│   └── delete/route.ts               # 删除
└── processingParams/                  # 固定参数API
    ├── get/route.ts                  # 获取配置
    └── update/route.ts               # 更新配置
```

#### 2.2 核心特性
- **Tab页设计**：使用Ant Design Tabs组件分离两个功能模块
- **统一错误处理**：使用`useAsyncError` Hook处理异步操作
- **完整CRUD操作**：支持加工费的增删改查
- **实时搜索筛选**：支持按名称、备注搜索和单位筛选
- **表单验证**：使用Zod进行前后端统一验证

### 3. 实现要点

#### 3.1 单文件组件结构
```typescript
export default function ProcessingFeeManagementPage() {
  // 错误处理Hook
  const { execute: executeFee, loading: feeLoading } = useAsyncError();
  const { execute: executeParams, loading: paramsLoading } = useAsyncError();

  // Tab状态管理
  const [activeTab, setActiveTab] = useState('fee');

  // 加工费数据状态
  const [feeList, setFeeList] = useState<ProcessingFee[]>([]);
  const [feeTotal, setFeeTotal] = useState(0);
  // ... 其他状态

  // 固定参数状态
  const [paramsData, setParamsData] = useState<ProcessingParams | null>(null);
  
  // 表单实例
  const [feeForm] = Form.useForm();
  const [paramsForm] = Form.useForm();

  // 数据获取和操作函数
  const fetchFeeList = async () => { /* ... */ };
  const fetchParamsData = async () => { /* ... */ };
  
  // 事件处理函数
  const handleFeeFormSubmit = async () => { /* ... */ };
  const handleParamsSave = async () => { /* ... */ };

  return (
    <div className="p-6">
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 加工费配置Tab */}
          <Tabs.TabPane tab="加工费配置" key="fee">
            {/* 搜索筛选 + 表格 */}
          </Tabs.TabPane>
          
          {/* 固定参数配置Tab */}
          <Tabs.TabPane tab="固定参数配置" key="params">
            {/* 参数配置表单 */}
          </Tabs.TabPane>
        </Tabs>
        
        {/* 编辑模态框 */}
        <Modal>
          {/* 加工费编辑表单 */}
        </Modal>
      </Card>
    </div>
  );
}
```

#### 3.2 API设计规范
```typescript
// 使用withValidation中间件进行参数验证
export const POST = withValidation(
  processingFeeListParamsSchema,
  async (request: NextRequest, validatedQuery: any) => {
    // 构建查询条件
    const where: Prisma.ProcessingFeeWhereInput = {
      isDel: false
    };

    // 搜索条件
    if (data.search) {
      where.OR = [
        { name: { contains: data.search } },
        { remark: { contains: data.search } }
      ];
    }

    // 返回分页数据
    return paginatedResponse(list, pagination, '获取成功');
  }
);
```

#### 3.3 错误处理模式
```typescript
// 使用统一的错误处理Hook
const { execute: executeFee, loading: feeLoading } = useAsyncError();

// 在操作函数中使用
const fetchFeeList = async () => {
  const result = await executeFee(async () => {
    return await processingFeeApi.getList(requestParams);
  }, '获取加工费列表');

  if (result) {
    setFeeList(result.list || []);
    setFeeTotal(result.pagination?.total || 0);
  }
};
```

### 4. 数据验证

#### 4.1 Zod验证规则
```typescript
// 加工费创建验证
export const createProcessingFeeSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  unit: z.enum(PROCESSING_FEE_UNITS),
  basePrice: z.number().min(0, '起步价不能为负数'),
  remark: z.string().max(500, '备注长度不能超过500个字符').optional()
});

// 固定参数验证
export const updateProcessingParamsSchema = z.object({
  pvcFilm: z.number().min(0, 'PVC贴膜价格不能为负数'),
  slottingSalary: z.number().min(0, '开槽工资不能为负数'),
  // ... 其他字段
});
```

### 5. 最佳实践

#### 5.1 状态管理
- 使用`useState`管理组件本地状态
- 合理拆分状态，避免单个状态过于复杂
- 使用`useEffect`处理副作用和数据加载

#### 5.2 表单处理
- 使用Ant Design Form组件的`form.setFieldsValue()`设置初始值
- 在模态框中使用`preserve={false}`避免表单缓存问题
- 使用`validateFields()`进行表单验证

#### 5.3 API调用
- 统一使用POST方法，符合项目规范
- 使用驼峰命名的API路径
- 实现完整的错误处理和加载状态

#### 5.4 用户体验
- 提供实时搜索和筛选功能
- 使用确认对话框进行删除操作
- 显示操作成功/失败的消息提示
- 合理的加载状态和分页功能

### 6. 注意事项

1. **单文件实现**：所有功能集中在一个文件中，便于维护和理解
2. **Tab页设计**：合理分离不同功能模块，提供清晰的用户界面
3. **错误处理**：使用项目统一的错误处理机制
4. **类型安全**：充分利用TypeScript的类型检查
5. **响应式设计**：确保在不同屏幕尺寸下的良好显示效果
6. **Tabs标准实现**：使用`items`属性方式而不是`<Tabs.TabPane>`组件，每个Tab内容必须用Card包装

#### 6.1 Tabs实现对比

**✅ 标准实现**（加工费模块采用）:
```typescript
// Tab内容配置
const tabItems = [
  {
    key: 'fee',
    label: '加工费配置',
    children: (
      <Card>
        {/* 搜索筛选 + 表格内容 */}
        <Row gutter={16} align="middle" style={{ marginBottom: 16 }}>
          {/* 搜索和操作按钮 */}
        </Row>
        <Table />
      </Card>
    )
  },
  {
    key: 'params',
    label: '固定参数配置',
    children: (
      <Card>
        <Form>
          {/* 表单内容 */}
        </Form>
      </Card>
    )
  }
];

return (
  <div>
    <Title level={2}>加工费管理</Title>
    <Tabs
      activeKey={activeTab}
      onChange={setActiveTab}
      items={tabItems}
    />
  </div>
);
```

**❌ 避免的混合实现**:
```typescript
// 不要混合使用 items 和 TabPane
<Tabs
  activeKey={activeTab}
  onChange={setActiveTab}
  items={[
    {
      key: 'fee',
      label: '加工费配置',
      children: <Card>内容</Card>
    }
  ]}
>
  <Tabs.TabPane tab="固定参数配置" key="params">
    <Card>内容</Card>
  </Tabs.TabPane>
</Tabs>
```

---

*加工费模块实现指南 - 2024年12月更新* 