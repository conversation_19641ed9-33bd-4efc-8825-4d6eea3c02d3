import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

/**
 * 用户端保存报价接口
 * 将用户的报价保存到数据库中
 */
const handler = async (request: AuthenticatedRequest) => {
  try {
    const body = await request.json();
    const { quotation, calculationState } = body;
    const userId = request.user?.userId;

    if (!userId) {
      return errorResponse(ErrorCode.UNAUTHORIZED, '用户未登录', null, 401);
    }

    // 保存用户报价到数据库
    // 注意：这里需要根据实际的数据库表结构来调整
    // 目前使用一个简化的保存逻辑
    const savedQuotation = {
      id: Date.now(),
      userId: userId,
      projectName: quotation.projectName,
      quantity: quotation.quantity,
      finalTotal: quotation.finalTotal,
      quotationData: JSON.stringify(quotation),
      calculationData: JSON.stringify(calculationState),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 这里应该实际保存到数据库
    // const result = await prisma.userQuotation.create({
    //   data: savedQuotation
    // });

    return successResponse(savedQuotation, '报价保存成功');
  } catch (error) {
    console.error('保存报价失败:', error);
    return errorResponse(ErrorCode.INTERNAL_ERROR, '保存报价失败', null, 500);
  }
};

// 用户端接口：需要登录但不需要管理员权限
export const POST = withAuth(handler, { requireAuth: true });
