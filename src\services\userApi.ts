// 用户管理相关 API 服务，封装所有用户管理相关的后端交互
import { resultApi } from '@/lib/utils/request';
import {
  UserListItem,
  CreateUserRequest,
  UpdateUserRequest,
  QueryUserParams,
  UpdateUserStateRequest
} from '@/types/user';
import { Result, PaginatedData } from '@/types/common';

/**
 * 用户管理相关 API 服务 - 使用Result类型
 */
export const userApi = {
  /**
   * 获取用户列表
   */
  getList: (params: QueryUserParams): Promise<Result<PaginatedData<UserListItem>>> => {
    return resultApi.post<PaginatedData<UserListItem>>('/api/v1/admin/users/getList', params);
  },

  /**
   * 获取用户详情
   */
  getDetail: (id: number): Promise<Result<UserListItem>> => {
    return resultApi.post<UserListItem>('/api/v1/admin/users/getDetail', { id });
  },

  /**
   * 创建用户
   */
  create: (data: CreateUserRequest): Promise<Result<UserListItem>> => {
    return resultApi.post<UserListItem>('/api/v1/admin/users/create', data);
  },

  /**
   * 更新用户
   */
  update: (data: UpdateUserRequest): Promise<Result<UserListItem>> => {
    return resultApi.post<UserListItem>('/api/v1/admin/users/update', data);
  },

  /**
   * 删除用户
   */
  delete: (id: number): Promise<Result<{ id: number }>> => {
    return resultApi.post<{ id: number }>('/api/v1/admin/users/delete', { id });
  },

  /**
   * 更新用户状态
   */
  updateState: (data: UpdateUserStateRequest): Promise<Result<UserListItem>> => {
    return resultApi.post<UserListItem>('/api/v1/admin/users/updateState', data);
  },

  /**
   * 批量删除用户
   */
  batchDelete: async (ids: number[]): Promise<Result<null>> => {
    const results = await Promise.allSettled(
      ids.map(id => userApi.delete(id))
    );

    const failedResults = results.filter(result => result.status === 'rejected' || !result.value.success);

    if (failedResults.length === 0) {
      return {
        success: true,
        data: null
      };
    }

    return {
      success: false,
      error: {
        code: 500,
        message: `批量删除失败，${failedResults.length}个用户删除失败`
      }
    };
  },

  /**
   * 批量更新用户状态
   */
  batchUpdateState: async (ids: number[], state: number): Promise<Result<null>> => {
    const results = await Promise.allSettled(
      ids.map(id => userApi.updateState({ id, state }))
    );

    const failedResults = results.filter(result => result.status === 'rejected' || !result.value.success);

    if (failedResults.length === 0) {
      return {
        success: true,
        data: null
      };
    }

    return {
      success: false,
      error: {
        code: 500,
        message: `批量更新失败，${failedResults.length}个用户状态更新失败`
      }
    };
  },
};
