'use client';

import React, { useEffect } from 'react';
import { Card, message, Alert } from 'antd';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';
import { Box } from '@/types/box';

// 复用管理后台的基础信息步骤组件
import BasicInfoStep from '@/app/admin/box/calculation/components/steps/BasicInfoStep';

interface UserBasicInfoStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
  onRecalculate: () => void;
  sourceBox?: Box | null;
}

/**
 * 用户端基础信息步骤组件
 * 复用管理后台组件，移除盒型选择功能（盒型从 quote 页面传递）
 */
export default function UserBasicInfoStep({
  state,
  onUpdate,
  sourceBox
}: UserBasicInfoStepProps) {

  // 自动初始化盒型信息
  useEffect(() => {
    if (sourceBox && (!state.basicInfo.name || state.basicInfo.name === '')) {
      // 自动填充基础信息
      const calculationName = `${sourceBox.name}_计算_${new Date().toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}`;

      // 处理动态属性，填充默认值
      const dynamicAttributes = (sourceBox.attributes || []).map(attr => ({
        ...attr,
        value: attr.value || 0
      }));

      const updatedBasicInfo = {
        name: calculationName,
        quantity: 1000, // 默认数量
        attributes: dynamicAttributes,
        parts: sourceBox.parts || []
      };

      console.log('自动初始化基础信息:', updatedBasicInfo);
      onUpdate.basicInfo(updatedBasicInfo);
      message.success(`已选择盒型：${sourceBox.name}，请检查并调整参数`);
    }
  }, [sourceBox, state.basicInfo.name, onUpdate]);

  return (
    <div className="user-basic-info-step">
      {/* 显示当前选择的盒型信息 */}
      {sourceBox ? (
        <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
          <div style={{ color: '#52c41a' }}>
            <h4 style={{ margin: 0, color: '#52c41a' }}>✅ 已选择盒型</h4>
            <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
              <strong>{sourceBox.name}</strong>
              {sourceBox.description && ` - ${sourceBox.description}`}
            </p>
            <p style={{ margin: '4px 0 0 0', color: '#52c41a', fontSize: '12px' }}>
              请在下方填写或调整计算参数
            </p>
          </div>
        </Card>
      ) : (
        <Card style={{ marginBottom: 16, backgroundColor: '#fff2e8', border: '1px solid #ffbb96' }}>
          <div style={{ color: '#fa8c16' }}>
            <h4 style={{ margin: 0, color: '#fa8c16' }}>⚠️ 未选择盒型</h4>
            <p style={{ margin: '8px 0 0 0', color: '#fa8c16' }}>
              请从包装报价页面选择盒型后再进行计算
            </p>
          </div>
        </Card>
      )}

      {/* 复用管理后台的基础信息组件 */}
      <BasicInfoStep
        state={state}
        onUpdate={onUpdate}
        onRecalculate={() => {}} // 空函数，用户端不需要手动触发重新计算
        sourceBox={sourceBox}
      />
    </div>
  );
}
