'use client';

import React, { useState } from 'react';
import { Card, Button, Collapse, Typography } from 'antd';
import { BugOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

interface DebugPanelProps {
  state: CalculationState;
  quotation?: any;
  onTestCalculation?: () => void;
}

/**
 * 调试面板组件
 * 仅在开发环境中显示，用于调试算价状态
 */
export default function DebugPanel({ state, quotation, onTestCalculation }: DebugPanelProps) {
  const [visible, setVisible] = useState(false);

  // 只在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card 
      style={{ 
        marginTop: 16, 
        border: '2px dashed #ff4d4f',
        backgroundColor: '#fff2f0'
      }}
      title={
        <div style={{ color: '#ff4d4f' }}>
          <BugOutlined /> 调试面板 (仅开发环境)
        </div>
      }
      extra={
        <Button 
          size="small" 
          icon={visible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={() => setVisible(!visible)}
        >
          {visible ? '隐藏' : '显示'}
        </Button>
      }
    >
      {visible && (
        <Collapse size="small">
          <Panel header="计算状态 (state)" key="state">
            <Paragraph>
              <Text strong>当前步骤:</Text> {state.currentStep}
            </Paragraph>
            <Paragraph>
              <Text strong>基础信息:</Text>
              <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
                {JSON.stringify(state.basicInfo, null, 2)}
              </pre>
            </Paragraph>
            <Paragraph>
              <Text strong>部件配置:</Text>
              <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
                {JSON.stringify(state.partConfig, null, 2)}
              </pre>
            </Paragraph>
            <Paragraph>
              <Text strong>拼版配置:</Text>
              <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
                {JSON.stringify(state.packagingConfig, null, 2)}
              </pre>
            </Paragraph>
            <Paragraph>
              <Text strong>材料配置:</Text>
              <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
                {JSON.stringify(state.materialConfig, null, 2)}
              </pre>
            </Paragraph>
          </Panel>
          
          <Panel header="报价结果 (quotation)" key="quotation">
            <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
              {quotation ? JSON.stringify(quotation, null, 2) : '暂无报价数据'}
            </pre>
          </Panel>
          
          <Panel header="状态检查" key="validation">
            <Paragraph>
              <Text strong>状态是否存在:</Text> {state ? '✅ 是' : '❌ 否'}
            </Paragraph>
            <Paragraph>
              <Text strong>基础信息是否存在:</Text> {state?.basicInfo ? '✅ 是' : '❌ 否'}
            </Paragraph>
            <Paragraph>
              <Text strong>数量是否有效:</Text> {
                state?.basicInfo?.quantity && state.basicInfo.quantity > 0 ? 
                `✅ 是 (${state.basicInfo.quantity})` : 
                `❌ 否 (${state?.basicInfo?.quantity || 'undefined'})`
              }
            </Paragraph>
            <Paragraph>
              <Text strong>属性数量:</Text> {state?.basicInfo?.attributes?.length || 0}
            </Paragraph>
            <Paragraph>
              <Text strong>部件数量:</Text> {state?.basicInfo?.parts?.length || 0}
            </Paragraph>
            {onTestCalculation && (
              <Paragraph>
                <Button
                  type="primary"
                  size="small"
                  onClick={onTestCalculation}
                  style={{ marginTop: 8 }}
                >
                  测试计算报价
                </Button>
              </Paragraph>
            )}
          </Panel>
        </Collapse>
      )}
    </Card>
  );
}
